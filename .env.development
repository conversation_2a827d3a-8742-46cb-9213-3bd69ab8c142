# 当前环境：dev（开发环境）、prod（生产环境）等
VITE_NODE_ENV=dev

# API 根地址
# VITE_API_ORIGIN=http://42.236.74.152:8150
# VITE_API_ORIGIN=http://192.168.0.11:8181
# VITE_API_ORIGIN=http://192.168.0.34:8181
VITE_API_ORIGIN=http://anchen.natapp1.cc

# API 路径前缀
VITE_API_PATH=/api

# 是否启用开发代理：1 启用，0 关闭 (注意该配置仅在 H5 环境中生效)
VITE_PROXY_USE=0

# 代理请求的前缀路径
VITE_PROXY_PATH=/proxy

# WebSocket
# VITE_SOCKET_URL=ws://42.236.74.152:8150/websocket
# VITE_SOCKET_URL=ws://192.168.0.11:8180/websocket
# VITE_SOCKET_URL=ws://192.168.0.34:8180/websocket
VITE_SOCKET_URL=ws://anchen.natapp1.cc/websocket

# 语音助手
VITE_API_COPILOT=http://42.236.74.152:8150
# VITE_API_COPILOT=http://anchen.natapp1.cc

# Live
# VITE_LIVE_PUSHER_URL=rtmp://42.236.74.152:8650
# VITE_LIVE_PUSHER_URL=rtmp://192.168.0.11:1935
# VITE_LIVE_PUSHER_URL=rtmp://192.168.0.60:1935
VITE_LIVE_PUSHER_URL=rtmp://6d0612af11864019.natapp.cc:1935