{"name": "glasses-app", "type": "commonjs", "version": "0.0.1", "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "description": "AR巡检", "author": {"name": "安晨", "email": "<EMAIL>", "url": "https://ancheninfo.com/", "address": "北京安晨信息技术有限公司"}, "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "uvm": "npx @dcloudio/uvm@latest", "dev:custom": "uni -h", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-harmony": "uni -p mp-harmony", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-harmony": "uni build -p mp-harmony", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union"}, "dependencies": {"@alova/adapter-uniapp": "~2.0.11", "@alova/mock": "~2.0.14", "@alova/shared": "~1.1.2", "@dcloudio/uni-app": "3.0.0-4070520250711001", "@dcloudio/uni-app-harmony": "3.0.0-4070520250711001", "@dcloudio/uni-app-plus": "3.0.0-4070520250711001", "@dcloudio/uni-components": "3.0.0-4070520250711001", "@dcloudio/uni-h5": "3.0.0-4070520250711001", "@dcloudio/uni-mp-alipay": "3.0.0-4070520250711001", "@dcloudio/uni-mp-baidu": "3.0.0-4070520250711001", "@dcloudio/uni-mp-harmony": "3.0.0-4070520250711001", "@dcloudio/uni-mp-jd": "3.0.0-4070520250711001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4070520250711001", "@dcloudio/uni-mp-lark": "3.0.0-4070520250711001", "@dcloudio/uni-mp-qq": "3.0.0-4070520250711001", "@dcloudio/uni-mp-toutiao": "3.0.0-4070520250711001", "@dcloudio/uni-mp-weixin": "3.0.0-4070520250711001", "@dcloudio/uni-mp-xhs": "3.0.0-4070520250711001", "@dcloudio/uni-quickapp-webview": "3.0.0-4070520250711001", "@uni-helper/uni-env": "~0.1.7", "@uni-helper/uni-promises": "~0.2.1", "@vueuse/core": "~12.8.2", "alova": "~3.2.11", "color": "~5.0.0", "crypto-js": "4.2.0", "enum-plus": "2.3.3", "es-toolkit": "~1.38.0", "fast-querystring": "1.1.2", "pinia": "~2.0.36", "pinia-plugin-persistedstate": "~3.2.1", "uniapp-router-next": "~1.2.7", "vue": "3.4.21", "vue-i18n": "9.1.9", "wot-design-uni": "~1.9.1", "z-paging": "~2.8.6"}, "devDependencies": {"@antfu/eslint-config": "~4.13.1", "@dcloudio/types": "3.4.8", "@dcloudio/uni-automator": "3.0.0-4070520250711001", "@dcloudio/uni-cli-shared": "3.0.0-4070520250711001", "@dcloudio/uni-stacktracey": "3.0.0-4070520250711001", "@dcloudio/uni-uts-v1": "3.0.0-4060620250520001", "@dcloudio/vite-plugin-uni": "3.0.0-4070520250711001", "@iconify-json/carbon": "~1.2.8", "@uni-helper/unocss-preset-uni": "~0.2.11", "@uni-helper/vite-plugin-uni-pages": "0.2.24", "@unocss-applet/reset": "0.7.8", "@unocss/eslint-plugin": "0.58.9", "@unocss/preset-icons": "0.58.9", "@unocss/transformer-directives": "0.58.9", "@unocss/vite": "0.58.9", "@vue/runtime-core": "3.4.21", "eslint": "~9.27.0", "eslint-plugin-format": "~1.0.1", "postcss-nested": "~7.0.2", "postcss-remove-inline-comments": "~0.0.2", "postcss-scss": "~4.0.9", "sass": "~1.59.3", "simple-git-hooks": "~2.13.0", "uni-parse-pages": "~0.0.1", "unocss": "0.58.9", "unocss-applet": "0.7.8", "unplugin-auto-import": "~19.2.0", "unplugin-uni-router": "~1.2.7", "vite": "5.2.8"}, "simple-git-hooks": {"pre-commit": "pnpm lint:fix && git add ."}}