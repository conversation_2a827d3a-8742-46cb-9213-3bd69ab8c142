/**
 * UniApp Vue3 路由工具
 * 基于 fast-querystring 的高性能查询字符串处理
 */

import * as qs from 'fast-querystring'
import { computed, getCurrentInstance, reactive } from 'vue'

/**
 * @typedef {object} RouterOptions
 * @property {string} path - 页面路径
 * @property {object} [query] - 页面参数对象
 * @property {object} [params] - 额外参数（用于传递复杂数据）
 */

/**
 * @typedef {object} RouteInfo
 * @property {string} path - 当前页面路径
 * @property {object} query - 当前页面查询参数
 * @property {object} params - 当前页面额外参数
 * @property {string} fullPath - 完整路径（包含查询参数）
 */

/**
 * @typedef {Function} GuardFunction
 * @param {RouteInfo} to - 目标路由信息
 * @param {RouteInfo} from - 来源路由信息
 * @returns {boolean|Promise<boolean>} 是否允许跳转
 */

/**
 * @typedef {Function} AfterGuardFunction
 * @param {RouteInfo} to - 目标路由信息
 * @param {RouteInfo} from - 来源路由信息
 */

// 全局路由状态
const globalRouterState = reactive({
  currentRoute: {
    path: '',
    query: {},
    params: {},
    fullPath: '',
  },
  history: [],
  beforeGuards: [],
  afterGuards: [],
})

/**
 * 路由工具类
 */
class RouterInstance {
  constructor() {
    this.beforeGuards = []
    this.afterGuards = []
    this.init()
  }

  /**
   * 初始化路由
   */
  init() {
    // 获取当前页面信息
    this.updateCurrentRoute()

    // 设置路由拦截器
    this.setupInterceptors()
  }

  /**
   * 更新当前路由信息
   */
  updateCurrentRoute() {
    try {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        const route = currentPage.route || currentPage.__route__ || ''
        const options = currentPage.options || {}

        globalRouterState.currentRoute = {
          path: `/${route}`,
          query: options,
          params: currentPage.routeParams || {},
          fullPath: this.buildFullPath(`/${route}`, options),
        }
      }
    }
    catch (error) {
      console.warn('获取当前路由信息失败:', error)
    }
  }

  /**
   * 构建完整路径
   * @param {string} path - 路径
   * @param {object} query - 查询参数
   * @returns {string} 完整路径
   */
  buildFullPath(path, query = {}) {
    if (Object.keys(query).length === 0) {
      return path
    }
    const queryString = qs.stringify(query)
    return `${path}?${queryString}`
  }

  /**
   * 解析路径和查询参数
   * @param {string|RouterOptions} to - 目标路由
   * @returns {RouterOptions} 解析后的路由选项
   */
  parseRoute(to) {
    if (typeof to === 'string') {
      const [path, queryString] = to.split('?')
      const query = queryString ? qs.parse(queryString) : {}
      return { path, query, params: {} }
    }
    return {
      path: to.path,
      query: to.query || {},
      params: to.params || {},
    }
  }

  /**
   * 设置路由拦截器
   */
  setupInterceptors() {
    // 拦截 navigateTo
    uni.addInterceptor('navigateTo', {
      invoke: async (args) => {
        const allowed = await this.runBeforeGuards(args.url, 'navigateTo')
        if (!allowed) {
          return false
        }
        return args
      },
      success: (res) => {
        this.runAfterGuards()
      },
    })

    // 拦截 redirectTo
    uni.addInterceptor('redirectTo', {
      invoke: async (args) => {
        const allowed = await this.runBeforeGuards(args.url, 'redirectTo')
        if (!allowed) {
          return false
        }
        return args
      },
      success: (res) => {
        this.runAfterGuards()
      },
    })

    // 拦截 reLaunch
    uni.addInterceptor('reLaunch', {
      invoke: async (args) => {
        const allowed = await this.runBeforeGuards(args.url, 'reLaunch')
        if (!allowed) {
          return false
        }
        return args
      },
      success: (res) => {
        this.runAfterGuards()
      },
    })

    // 拦截 switchTab
    uni.addInterceptor('switchTab', {
      invoke: async (args) => {
        const allowed = await this.runBeforeGuards(args.url, 'switchTab')
        if (!allowed) {
          return false
        }
        return args
      },
      success: (res) => {
        this.runAfterGuards()
      },
    })
  }

  /**
   * 运行前置守卫
   * @param {string} url - 目标URL
   * @param {string} type - 跳转类型
   * @returns {Promise<boolean>} 是否允许跳转
   */
  async runBeforeGuards(url, type) {
    try {
      const [path, queryString] = url.split('?')
      const query = queryString ? qs.parse(queryString) : {}

      const to = {
        path,
        query,
        params: {},
        fullPath: url,
      }

      const from = { ...globalRouterState.currentRoute }

      // 执行全局前置守卫
      for (const guard of this.beforeGuards) {
        const result = await guard(to, from)
        if (result === false) {
          return false
        }
      }

      return true
    }
    catch (error) {
      console.error('路由前置守卫执行失败:', error)
      return false
    }
  }

  /**
   * 运行后置守卫
   */
  async runAfterGuards() {
    try {
      // 延迟执行，确保页面已经切换
      setTimeout(() => {
        const from = { ...globalRouterState.currentRoute }
        this.updateCurrentRoute()
        const to = { ...globalRouterState.currentRoute }

        // 执行全局后置守卫
        this.afterGuards.forEach((guard) => {
          try {
            guard(to, from)
          }
          catch (error) {
            console.error('路由后置守卫执行失败:', error)
          }
        })
      }, 100)
    }
    catch (error) {
      console.error('后置守卫执行失败:', error)
    }
  }

  /**
   * 页面跳转 - 保留当前页面
   * @param {string|RouterOptions} to - 目标路由
   * @returns {Promise<void>}
   */
  async push(to) {
    const route = this.parseRoute(to)
    const url = this.buildFullPath(route.path, route.query)

    return new Promise((resolve, reject) => {
      uni.navigateTo({
        url,
        success: () => {
          // 存储额外参数
          if (route.params && Object.keys(route.params).length > 0) {
            const pages = getCurrentPages()
            if (pages.length > 0) {
              pages[pages.length - 1].routeParams = route.params
            }
          }
          resolve()
        },
        fail: reject,
      })
    })
  }

  /**
   * 页面重定向 - 关闭当前页面
   * @param {string|RouterOptions} to - 目标路由
   * @returns {Promise<void>}
   */
  async replace(to) {
    const route = this.parseRoute(to)
    const url = this.buildFullPath(route.path, route.query)

    return new Promise((resolve, reject) => {
      uni.redirectTo({
        url,
        success: () => {
          if (route.params && Object.keys(route.params).length > 0) {
            const pages = getCurrentPages()
            if (pages.length > 0) {
              pages[pages.length - 1].routeParams = route.params
            }
          }
          resolve()
        },
        fail: reject,
      })
    })
  }

  /**
   * 重新启动应用 - 关闭所有页面
   * @param {string|RouterOptions} to - 目标路由
   * @returns {Promise<void>}
   */
  async replaceAll(to) {
    const route = this.parseRoute(to)
    const url = this.buildFullPath(route.path, route.query)

    return new Promise((resolve, reject) => {
      uni.reLaunch({
        url,
        success: () => {
          if (route.params && Object.keys(route.params).length > 0) {
            const pages = getCurrentPages()
            if (pages.length > 0) {
              pages[pages.length - 1].routeParams = route.params
            }
          }
          resolve()
        },
        fail: reject,
      })
    })
  }

  /**
   * 返回上一页
   * @param {number} [delta] - 返回页面层数
   * @returns {Promise<void>}
   */
  async back(delta = 1) {
    return new Promise((resolve, reject) => {
      uni.navigateBack({
        delta,
        success: resolve,
        fail: reject,
      })
    })
  }

  /**
   * 切换到 tabBar 页面
   * @param {string|RouterOptions} to - 目标路由
   * @returns {Promise<void>}
   */
  async switchTab(to) {
    const route = this.parseRoute(to)

    return new Promise((resolve, reject) => {
      uni.switchTab({
        url: route.path,
        success: resolve,
        fail: reject,
      })
    })
  }

  /**
   * 注册全局前置守卫
   * @param {GuardFunction} guard - 守卫函数
   */
  beforeEach(guard) {
    this.beforeGuards.push(guard)
  }

  /**
   * 注册全局后置守卫
   * @param {AfterGuardFunction} guard - 守卫函数
   */
  afterEach(guard) {
    this.afterGuards.push(guard)
  }

  /**
   * 获取当前路由信息
   * @returns {RouteInfo} 当前路由信息
   */
  getCurrentRoute() {
    return { ...globalRouterState.currentRoute }
  }
}

// 全局路由实例
let globalRouter = null

/**
 * 创建路由实例
 * @returns {RouterInstance & { install: Function }}
 */
export function createRouter() {
  const router = new RouterInstance()

  return {
    ...router,
    /**
     * Vue 插件安装方法
     * @param {object} app - Vue 应用实例
     */
    install(app) {
      globalRouter = router

      // 全局属性
      app.config.globalProperties.$router = router
      app.config.globalProperties.$route = computed(() => globalRouterState.currentRoute)

      // 提供注入
      app.provide('router', router)
      app.provide('route', computed(() => globalRouterState.currentRoute))
    },
  }
}

/**
 * 获取路由实例（组合式 API）
 * @returns {RouterInstance} 路由实例
 */
export function useRouter() {
  const instance = getCurrentInstance()

  if (instance && instance.appContext.provides.router) {
    return instance.appContext.provides.router
  }

  if (globalRouter) {
    return globalRouter
  }

  throw new Error('请先安装路由插件或确保在 Vue 组件中调用 useRouter')
}

/**
 * 获取当前路由信息（组合式 API）
 * @returns {import('vue').ComputedRef<RouteInfo>} 当前路由的响应式引用
 */
export function useRoute() {
  const instance = getCurrentInstance()

  if (instance && instance.appContext.provides.route) {
    return instance.appContext.provides.route
  }

  // 返回响应式的当前路由信息
  return computed(() => {
    if (globalRouter) {
      globalRouter.updateCurrentRoute()
    }
    return globalRouterState.currentRoute
  })
}

/**
 * 工具函数：构建查询字符串
 * @param {object} query - 查询参数对象
 * @returns {string} 查询字符串
 */
export function buildQuery(query) {
  return qs.stringify(query)
}

/**
 * 工具函数：解析查询字符串
 * @param {string} queryString - 查询字符串
 * @returns {object} 解析后的对象
 */
export function parseQuery(queryString) {
  return qs.parse(queryString)
}

// 页面生命周期钩子（在页面中使用）
export const routerLifecycle = {
  /**
   * 页面加载时更新路由信息
   */
  onLoad() {
    if (globalRouter) {
      globalRouter.updateCurrentRoute()
    }
  },

  /**
   * 页面显示时更新路由信息
   */
  onShow() {
    if (globalRouter) {
      globalRouter.updateCurrentRoute()
    }
  },
}
