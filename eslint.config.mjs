import { createRequire } from 'node:module'
import antfu from '@antfu/eslint-config'

const require = createRequire(import.meta.url)

const autoImport = require('./.eslintrc-auto-import.json')

export default antfu({
  unocss: true,
  formatters: {
    css: true,
    html: true,
    markdown: 'prettier',
  },
  ignores: [
    '**/uni_modules',
    '**/nativeplugins',
  ],
}, {
  languageOptions: {
    globals: {
      uni: 'readonly',
      plus: 'readonly',
      getCurrentPages: 'readonly',
      ...autoImport.globals,
    },
  },
  rules: {
    'node/prefer-global/process': 'off',

    'unused-imports/no-unused-vars': 'off',
    'no-console': 'off',
    'new-cap': 'off',
    'prefer-promise-reject-errors': 'off',
    'regexp/no-super-linear-backtracking': 'off',
    'regexp/optimal-quantifier-concatenation': 'off',

    'vue/no-unused-refs': 'off',
    'vue/no-unused-vars': 'off',
    'vue/html-self-closing': 'off',
    'vue/custom-event-name-casing': 'off',

    'jsdoc/check-param-names': 'off',
    'jsdoc/require-returns-description': 'off',

    'ts/no-use-before-define': 'off',

  },
})
