import { defineStore } from 'pinia'
import { getUserInfo } from '@/api/user/index.js'

export const useUserStore = defineStore(
  'user',
  () => {
    const socketStore = useSocketStore()

    const userInfo = ref(null)

    const loginInfo = ref(null)
    const token = ref('')

    const workerInfo = computed(() => userInfo.value?.loginWorkerInfoVo)
    const deviceInfo = computed(() => userInfo.value?.loginGlassDeviceVo)

    const userId = computed(() => workerInfo.value?.id)
    const userNo = computed(() => workerInfo.value?.workerNum)

    const deviceId = computed(() => userInfo.value?.clientId)
    const deviceNo = computed(() => deviceInfo.value?.deviceNo)

    async function login({ toHome = false, ...params } = {}) {
      const device = await uni.getDeviceInfo()

      const loginParams = {
        grantType: 'api',
        clientId: process.env.VITE_APP_DEVICE_ID || device.deviceId,
        ...(loginInfo.value || {}),
        ...params,
      }

      const res = await socketStore.sendSync({
        uri: 'login',
        params: loginParams,
      })

      if (res.code !== Number(process.env.VITE_API_SUCCESS_CODE)) {
        throw new Error(res.msg)
      }

      const accessToken = res?.data?.apiAccessToken

      if (!accessToken) {
        throw new Error('获取 TOKEN 失败')
      }

      token.value = accessToken

      userInfo.value = res.data

      loginInfo.value = {
        ...(loginInfo.value || {}),
        ...loginParams,
      }

      if (toHome) {
        uni.reLaunch({
          url: '/pages/index/index',
        })
      }
    }

    function logout({ toLogin } = {}) {
      token.value = ''
      userInfo.value = null

      if (toLogin) {
        uni.reLaunch({
          url: '/pages/login/index',
        })
      }
    }

    async function getUserData() {
      const res = await getUserInfo()

      userInfo.value = res.data
    }

    return {
      loginInfo,
      token,
      userInfo,
      deviceInfo,
      workerInfo,
      userId,
      userNo,
      deviceId,
      deviceNo,
      login,
      logout,
      getUserData,
    }
  },
  {
    persist: true,
  },
)