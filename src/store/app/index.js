import { defineStore } from 'pinia'
import { primaryColor as defaultPrimaryColor } from '@/settings/index.mjs'

export const useAppStore = defineStore(
  'app',
  () => {
    const themeModel = {
      primary: { color: defaultPrimaryColor, name: '默认' },
    }

    const currentTheme = ref('primary')

    const currentThemeInfo = computed(() => themeModel[currentTheme.value] || themeModel.primary)

    const primaryColor = computed(() => currentThemeInfo.value?.color || defaultPrimaryColor)

    return {
      themeModel,
      currentTheme,
      currentThemeInfo,
      primaryColor,
    }
  },
  {
    persist: true,
  },
)
