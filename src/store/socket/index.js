/**
 * @typedef {'disconnected' | 'connecting' | 'connected' | 'error' | 'reconnecting'} ConnectionStatus
 */

/**
 * @typedef {object} ConnectOptions
 * @property {boolean} [autoReconnect=true] - 是否开启自动重连
 * @property {number} [reconnectInterval=2000] - 初始重连间隔 (毫秒)
 * @property {number} [maxReconnectAttempts=Number.MAX_VALUE] - 最大重连尝试次数
 * @property {number} [maxReconnectInterval=30000] - 最大重连间隔 (毫秒)
 * @property {boolean} [heartbeatDetection=true] - 是否启用心跳检测
 * @property {number} [heartbeatInterval=15000] - 心跳间隔 (毫秒)
 * @property {number} [heartbeatTimeout=10000] - 心跳超时时间 (毫秒)
 */

/**
 * WebSocket 统一管理 Store
 */
export const useSocketStore = defineStore('socket', () => {
  /** @type {UniApp.SocketTask | null} */
  let socketTask = null
  /** @type {import('vue').Ref<ConnectionStatus>} */
  const status = ref('disconnected')

  // --- Reconnection State ---
  /** @type {import('vue').Ref<ConnectOptions & {url: string}>} */
  const connectOptions = ref({
    url: process.env.VITE_SOCKET_URL,
    heartbeatDetection: true,
    autoReconnect: true,
    reconnectInterval: 2000,
    maxReconnectAttempts: Number.MAX_VALUE,
    maxReconnectInterval: 30000,
    heartbeatInterval: 15000,
    heartbeatTimeout: 10000,
  })
  let reconnectAttempts = 0
  /** @type {number | null} */
  let reconnectTimer = null
  let isManualClose = false

  // --- Message & Sync Request State ---
  /** @type {import('vue').Ref<any>} */
  const message = ref(null)
  /**
   * @private
   * @type {Map<string, { resolve: (value: any) => void, reject: (reason?: any) => void, timer: number }>}
   */
  const pendingRequests = new Map()

  // --- Enhanced Heartbeat State ---
  /** @type {number | null} */
  let heartbeatInterval = null
  /** @type {number | null} */
  let heartbeatTimeoutTimer = null
  /** @type {number} */
  let lastHeartbeatTime = 0
  /** @type {boolean} */
  let waitingForPong = false

  const onErrorCallback = shallowRef()

  // --- Getters (Computed) ---
  const isConnected = computed(() => status.value === 'connected')
  const connectionStatus = readonly(status)
  const latestMessage = readonly(message)

  const userStore = useUserStore()

  uni.$on('socket-send', (data) => {
    send(data)
  })

  // --- Private Methods ---

  /** 清理所有定时器和暂存的请求 */
  function cleanup() {
    // 停止心跳
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
      heartbeatInterval = null
    }

    // 停止心跳超时检测
    if (heartbeatTimeoutTimer) {
      clearTimeout(heartbeatTimeoutTimer)
      heartbeatTimeoutTimer = null
    }

    // 停止重连
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    // 重置心跳状态
    waitingForPong = false
    lastHeartbeatTime = 0

    // 拒绝所有待处理的请求
    pendingRequests.forEach((request) => {
      clearTimeout(request.timer)
      request.reject(new Error('WebSocket connection closed or reset.'))
    })
    pendingRequests.clear()
  }

  /** 内部实际执行连接的方法 */
  function _doConnect() {
    console.log(`Attempting to connect to ${connectOptions.value.url}... (Attempt: ${reconnectAttempts + 1})`)
    status.value = 'connecting'

    socketTask = uni.connectSocket({
      url: connectOptions.value.url,
      success: () => {},
      fail: (err) => {
        console.error('uni.connectSocket API call failed!', err)
        status.value = 'error'
        // 如果 API 调用本身就失败了，也尝试重连
        handleUnexpectedClose()
      },
    })
    registerEventListeners()
  }

  /** 处理意外断线，并根据配置决定是否重连 */
  function handleUnexpectedClose() {
    if (isManualClose) {
      console.log('Manual close detected, skipping reconnection.')
      return // 如果是手动关闭，则不重连
    }

    if (!connectOptions.value.autoReconnect) {
      console.log('Auto-reconnect is disabled.')
      status.value = 'error'
      return
    }

    if (reconnectAttempts >= connectOptions.value.maxReconnectAttempts) {
      console.error(`Max reconnect attempts (${connectOptions.value.maxReconnectAttempts}) reached. Stopping.`)
      status.value = 'error'
      uni.showToast({
        title: '连接服务器失败，请检查网络',
        icon: 'none',
        duration: 3000,
      })
      return
    }

    // 使用指数退避算法计算重连延迟，但限制最大延迟
    const baseDelay = connectOptions.value.reconnectInterval
    const exponentialDelay = baseDelay * 2 ** reconnectAttempts
    const delay = Math.min(exponentialDelay, connectOptions.value.maxReconnectInterval)

    console.log(`Connection lost. Attempting to reconnect in ${delay / 1000}s... (Attempt ${reconnectAttempts + 1}/${connectOptions.value.maxReconnectAttempts})`)

    status.value = 'reconnecting'

    reconnectTimer = setTimeout(() => {
      reconnectAttempts++

      // 只在前几次重连时显示 toast，避免过于频繁的提示
      if (reconnectAttempts <= 3) {
        uni.showToast({
          title: `连接服务器失败, 正在尝试重连`,
          icon: 'none',
          duration: 2000,
        })
      }

      _doConnect()
    }, delay)
  }

  /** 启动心跳机制 */
  function startHeartbeat() {
    if (!connectOptions.value.heartbeatDetection) {
      console.log('Heartbeat detection is disabled.')
      return
    }

    // 清除之前的心跳定时器
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
    }
    if (heartbeatTimeoutTimer) {
      clearTimeout(heartbeatTimeoutTimer)
    }

    console.log(`Starting heartbeat with interval: ${connectOptions.value.heartbeatInterval}ms`)

    // 重置心跳状态
    waitingForPong = false
    lastHeartbeatTime = Date.now()

    heartbeatInterval = setInterval(() => {
      sendHeartbeat()
    }, connectOptions.value.heartbeatInterval)
  }

  /** 发送心跳包 */
  function sendHeartbeat() {
    if (!isConnected.value) {
      console.warn('Cannot send heartbeat: WebSocket not connected')
      return
    }

    if (waitingForPong) {
      console.warn('Previous heartbeat still waiting for response, connection may be unstable')
      // 如果上一个心跳还在等待响应，可能连接有问题
      handleHeartbeatTimeout()
      return
    }

    console.log('💓 Sending heartbeat ping...')
    waitingForPong = true
    lastHeartbeatTime = Date.now()

    // 设置心跳超时检测
    heartbeatTimeoutTimer = setTimeout(() => {
      handleHeartbeatTimeout()
    }, connectOptions.value.heartbeatTimeout)

    // 发送心跳包
    send({ uri: 'ping', token: userStore.token })
  }

  /** 处理心跳响应 */
  function handleHeartbeatResponse() {
    if (!waitingForPong) {
      console.warn('Received unexpected pong response')
      return
    }

    const responseTime = Date.now() - lastHeartbeatTime
    console.log(`💓 Received heartbeat pong (${responseTime}ms)`)

    waitingForPong = false

    if (heartbeatTimeoutTimer) {
      clearTimeout(heartbeatTimeoutTimer)
      heartbeatTimeoutTimer = null
    }
  }

  /** 处理心跳超时 */
  function handleHeartbeatTimeout() {
    console.error('💔 Heartbeat timeout detected, connection may be lost')
    waitingForPong = false

    if (heartbeatTimeoutTimer) {
      clearTimeout(heartbeatTimeoutTimer)
      heartbeatTimeoutTimer = null
    }

    // 心跳超时，认为连接已断开，触发重连
    if (socketTask) {
      socketTask.close({ code: 1000, reason: 'Heartbeat timeout' })
    }
  }

  function registerEventListeners() {
    if (!socketTask)
      return

    socketTask.onOpen(() => {
      console.log('✅ WebSocket connected successfully!')
      status.value = 'connected'

      checkAutoLogin()

      // 连接成功后，重置重连状态
      reconnectAttempts = 0
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
        reconnectTimer = null
      }

      // 启动心跳机制
      startHeartbeat()
    })

    socketTask.onMessage((res) => {
      let parsedData
      try {
        parsedData = JSON.parse(res.data)
      }
      catch (e) {
        parsedData = res.data
      }

      console.log('📨 Received socket message:', parsedData)

      const uri = parsedData.uri

      // 处理心跳响应
      if (uri === 'pong') {
        handleHeartbeatResponse()
        return
      }

      uni.$emit('socket-message', parsedData)

      // 使用 uri 匹配 sync-request 的响应
      if (uri && pendingRequests.has(uri)) {
        const request = pendingRequests.get(uri)
        if (request) {
          clearTimeout(request.timer)
          request.resolve(parsedData)
          pendingRequests.delete(uri)
        }
      }
      else {
        message.value = parsedData
      }
    })

    socketTask.onClose((res) => {
      console.log('🔌 WebSocket connection closed.', res.reason)
      status.value = 'disconnected'
      cleanup()
      handleUnexpectedClose() // 处理意外关闭
    })

    socketTask.onError((err) => {
      onErrorCallback.value()
      console.error('❌ WebSocket error:', err)
      status.value = 'error'
      // 错误也可能导致连接关闭，也触发重连检查
      // onClose 通常也会被触发，但为保险起见，这里也调用
      handleUnexpectedClose()
    })
  }

  /** 检查网络连接状态 */
  function checkNetworkStatus() {
    return new Promise((resolve) => {
      uni.getNetworkType({
        success: (res) => {
          const isConnected = res.networkType !== 'none'
          console.log(`Network status: ${res.networkType}, connected: ${isConnected}`)
          resolve(isConnected)
        },
        fail: () => {
          console.warn('Failed to get network status, assuming connected')
          resolve(true)
        },
      })
    })
  }

  /**
   * 建立 WebSocket 连接
   * @param {ConnectOptions} [options] - 连接选项
   */
  async function connect(options = {}) {
    if (status.value === 'connected' || status.value === 'connecting') {
      console.warn('WebSocket is already connected or connecting.')
      return
    }

    // 检查网络状态
    const networkAvailable = await checkNetworkStatus()
    if (!networkAvailable) {
      console.error('No network connection available')
      status.value = 'error'
      uni.showToast({
        title: '网络连接不可用',
        icon: 'none',
        duration: 2000,
      })
      return
    }

    isManualClose = false
    reconnectAttempts = 0

    // 合并配置选项
    Object.assign(connectOptions.value, options)

    console.log('Starting WebSocket connection with options:', connectOptions.value)
    _doConnect()
  }

  /** 发送普通消息 */
  function send(data) {
    if (!isConnected.value || !socketTask) {
      console.error('WebSocket is not connected. Cannot send message.')
      return
    }

    const messageToSend = typeof data === 'object' ? JSON.stringify(data) : data

    socketTask.send({
      data: messageToSend,
      fail: (err) => {
        console.error('Failed to send message:', err)
      },
    })
  }

  /**
   * 主动关闭 WebSocket 连接 (不会触发重连)
   */
  function close() {
    if (!socketTask)
      return

    console.log('👋 Manually closing WebSocket connection...')
    isManualClose = true
    cleanup() // 清理定时器和请求
    socketTask.close({ code: 1000, reason: 'Manual disconnection' })
    status.value = 'disconnected'
  }

  /**
   * 发送消息并等待响应 (Promise-based)
   * **需要业务配合**: data 对象中必须包含 `uri` 字段.
   * @param {object & {uri: string}} data - 需要发送的数据对象, 必须包含 uri.
   * @param {number} [timeout] - 等待响应的超时时间 (毫秒).
   * @returns {Promise<any>} 返回获取到的对应的异步数据
   */
  function sendSync(data, timeout = 10000) {
    if (!isConnected.value)
      return Promise.reject(new Error('WebSocket is not connected.'))

    const uri = data.uri
    if (!uri)
      return Promise.reject(new Error('sendSync requires a "uri" key in the data object.'))
    if (pendingRequests.has(uri))
      return Promise.reject(new Error(`Duplicate request with uri: ${uri}`))

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        if (pendingRequests.has(uri)) {
          pendingRequests.get(uri)?.reject(new Error(`Request '${uri}' timed out after ${timeout}ms`))
          pendingRequests.delete(uri)
        }
      }, timeout)

      pendingRequests.set(uri, { resolve, reject, timer })

      try {
        send(data)
      }
      catch (error) {
        pendingRequests.get(uri)?.reject(error)
        pendingRequests.delete(uri)
        clearTimeout(timer)
      }
    })
  }

  function onError(callback) {
    onErrorCallback.value = callback
  }

  /** 获取连接统计信息 */
  function getConnectionStats() {
    return {
      status: status.value,
      reconnectAttempts,
      isManualClose,
      waitingForPong,
      lastHeartbeatTime,
      pendingRequestsCount: pendingRequests.size,
      connectOptions: { ...connectOptions.value },
    }
  }

  /** 重置连接状态 */
  function resetConnection() {
    console.log('Resetting WebSocket connection...')
    close()
    setTimeout(() => {
      connect()
    }, 1000)
  }

  /** 强制重连 */
  function forceReconnect() {
    console.log('Forcing WebSocket reconnection...')
    isManualClose = false
    reconnectAttempts = 0

    if (socketTask) {
      socketTask.close({ code: 1000, reason: 'Force reconnect' })
    }
    else {
      connect()
    }
  }

  async function checkAutoLogin() {
    if (!userStore.loginInfo) {
      uni.reLaunch({ url: '/pages/login/index' })
      return false
    }

    uni.showLoading({
      title: '连接中',
    })

    let logged = false

    try {
      await userStore.login()
      logged = true
    }
    catch (error) {
      console.log('error', error)
      uni.showToast({
        title: error.message || '自动登录失败',
        icon: 'none',
        duration: 1000,
      })
    }

    uni.hideLoading()

    if (logged) {
      return false
    }
  }

  return {
    // 状态相关
    connectionStatus,
    latestMessage,
    isConnected,

    // 连接管理
    connect,
    close,
    resetConnection,
    forceReconnect,

    // 消息发送
    send,
    sendSync,

    // 事件处理
    onError,

    // 调试和监控
    getConnectionStats,
  }
})
