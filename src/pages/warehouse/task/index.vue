<script setup>
import { getMwhTaskList } from '@/api/warehouse/index.js'
import { warehouseTaskStatusEnum } from '@/dicts/index.js'
import { numberToChinese } from '@/utils/index.js'

const userStore = useUserStore()

const taskList = ref([])

const prompts = computed(() => {
  const value = []

  value.push({
    label: '打开第 1 项',
    glassesOnly: true,
    action: () => {
      onCreateTask()
    },
  })

  return value
})

onShow(() => {
  getTaskList()
})

async function getTaskList() {
  const params = {
    userNo: userStore.userNo,
    pageNum: 0,
    pageSize: 500,
  }

  const res = await getMwhTaskList(params).send(true)

  taskList.value = res.rows
}

function onCreateTask() {
  uni.navigateTo({
    url: '/pages/warehouse/task/create',
  })
}

function statusStyle(info) {
  if (['1'].includes(info.status)) {
    return 'text-primary-500'
  }
  else if (['2'].includes(info.status)) {
    return 'text-orange-500'
  }
  else {
    return 'text-gray-500'
  }
}

function onInfoClick(info) {
  if (['1'].includes(info.status)) {
    uni.navigateTo({
      url: `/pages/warehouse/task/receive?taskId=${info.id}`,
    })
    return false
  }

  if (['2'].includes(info.status)) {
    uni.navigateTo({
      url: `/pages/warehouse/task/return?taskId=${info.id}`,
    })
    return false
  }

  uni.navigateTo({
    url: `/pages/warehouse/task/info?taskId=${info.id}`,
  })
}
</script>

<template>
  <app-layout :prompts="prompts">
    <view class="h-full flex flex-col">
      <view class="flex-none flex items-center justify-between">
        <view class="flex items-center space-x-1">
          <view class="app-marker"></view>
          <view class="text-xs">
            料库任务列表
          </view>
        </view>

        <view class="flex items-center space-x-1 text-primary-500">
          <view class="i-carbon-camera size-[36rpx]"></view>
          <view class="text-xs" asia-label="新建" @click="onCreateTask">
            新建
          </view>
        </view>
      </view>

      <view class="flex-1 h-0 overflow-hidden mt-2 relative">
        <scroll-view class="size-full" scroll-y>
          <view class="w-full space-y-1">
            <view v-for="(item, index) of taskList" :key="index" :aria-label="`打开第${numberToChinese(index + 1)}项`" class="bg-[#242a38] rounded-sm py-1 px-2 w-full flex items-center space-x-3" @click="onInfoClick(item)">
              <view class="flex-none w-6 text-center text-xs truncate">
                {{ index + 1 }}
              </view>

              <view class="flex-1 w-0 text-xs truncate">
                {{ item.taskNum }}
              </view>

              <text class="flex-none w-12 truncate text-gray-400 text-2xs">
                {{ item.userName }}
              </text>

              <view class="flex-none w-8 whitespace-nowrap text-2xs" :class="statusStyle(item)">
                {{ warehouseTaskStatusEnum[item.status] }}
              </view>

              <view class="flex-none w-22 text-right text-2xs text-gray-400 truncate">
                {{ item.createTime }}
              </view>
            </view>

            <view v-if="!taskList.length" class="absolute inset-center text-gray-400 text-sm">
              暂无数据
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </app-layout>
</template>

<style>
</style>
