<script setup>
import markerDanger from '~@assets/images/warehouse/marker-danger.png'
import markerSuccess from '~@assets/images/warehouse/marker-success.png'
import { postMwhTaskReceive, postMwhTaskReset } from '@/api/warehouse/index.js'
import { doubleFormatter, getStoreQuery } from '@/utils/index.js'
import ReceiveSuccess from './components/receive-success/index.vue'

const receiveSuccessRef = ref()

const resultList = ref([])

const currentAction = ref('')

const queryParams = ref({})

const diffFlag = computed(() => {
  const value = resultList.value.some((item) => {
    return item.status !== '1'
  })

  return value
})

const diffList = computed(() => {
  const value = resultList.value.filter((item) => {
    return item.status !== '1'
  })

  return value
})

const diffCount = computed(() => {
  const value = diffList.value.length

  return value
})

const markerIcon = computed(() => diffFlag.value ? markerDanger : markerSuccess)

const prompts = computed(() => {
  const value = []

  if (['success'].includes(currentAction.value)) {
    value.push({
      label: '返回列表',
      action: () => {
        uni.redirectTo({
          url: '/pages/warehouse/task/index',
        })
      },
    })
  }
  else {
    value.push({
      label: '完成领取',
      action: () => {
        onSubmitClick()
      },
    })

    value.push({
      label: '继续识别',
      action: () => {
        uni.reLaunch({
          url: `/pages/warehouse/task/receive?taskId=${queryParams.value.taskId}&openCamera=Y`,
        })
      },
    })

    value.push({
      label: '重置结果',
      action: async () => {
        const res = await postMwhTaskReset({
          taskId: queryParams.value.taskId,
        }).send(true)

        resultList.value = res.data

        uni.showToast({
          title: res.msg || '重置成功',
          icon: 'none',
        })
      },
    })
  }

  return value
})

onLoad((query) => {
  queryParams.value = query
  resultList.value = getStoreQuery(query.resultId, false)
})

async function onSubmitClick() {
  const params = {
    id: queryParams.value.taskId,
  }

  await postMwhTaskReceive(params)

  receiveSuccessRef.value.open(diffList.value)

  currentAction.value = 'success'
}

function onBackClick() {
  if (['success'].includes(currentAction.value)) {
    currentAction.value = ''
    uni.reLaunch({
      url: '/pages/warehouse/task/index',
    })
    return false
  }

  uni.navigateBack()
}
</script>

<template>
  <app-layout :prompts="prompts" @back-click="onBackClick">
    <template #content-top>
      <view class="flex items-center justify-center space-x-1 mt-1">
        <image
          :src="markerIcon"
          mode="aspectFit"
          class="size-3 flex-none"
        />
        <view class="text-2xs flex-none">
          本次领取清单共识别 {{ resultList.length }} 类物品,
          <text v-if="diffFlag" class="text-[#E4544A]">
            与物料单有 {{ diffCount }} 类不一致
          </text>

          <text v-else class="text-[#28E6B6]">
            与物料单一致
          </text>
        </view>
        <image
          :src="markerIcon"
          mode="aspectFit"
          class="size-3 flex-none"
        />
      </view>
    </template>

    <view class="size-full overflow-hidden mt-2">
      <wd-table :data="resultList" height="100%" :border="false" custom-class="wd-table--designer">
        <wd-table-col prop="utensilName" label="工具" width="30%"></wd-table-col>
        <wd-table-col prop="utensilCount" label="计划数量" width="20%" align="center">
          <template #value="{ row }">
            {{ doubleFormatter(row.utensilName, row.utensilCount) }}
          </template>
        </wd-table-col>
        <wd-table-col prop="receiveCount" label="领取数量" width="30%" align="center">
          <template #value="{ row }">
            {{ row.receiveCount || 0 }}
          </template>
        </wd-table-col>
        <wd-table-col prop="status" label="领取状态" width="20%" align="left">
          <template #value="{ row }">
            <view class="flex items-center space-x-1">
              <view class="size-[24rpx] flex-none" :class="row.utensilCount === row.receiveCount ? 'i-carbon-checkmark-outline text-green-500' : 'i-carbon-close-outline text-red-500'"></view>
              <view class="flex-none">
                {{ row.utensilCount === row.receiveCount ? '一致' : '不一致' }}
              </view>
            </view>
          </template>
        </wd-table-col>
      </wd-table>
    </view>

    <ReceiveSuccess ref="receiveSuccessRef" />
  </app-layout>
</template>

<style>
</style>
