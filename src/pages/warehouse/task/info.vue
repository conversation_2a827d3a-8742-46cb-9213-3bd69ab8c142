<script setup>
import { getMwhMaterialList, getMwhTask } from '@/api/warehouse/index.js'
import InfoMaterial from './components/info-material/index.vue'
import InfoOrder from './components/info-order/index.vue'

const tabModel = [
  {
    label: '工单信息',
  },
  {
    label: '工器具信息',
  },
]

const tabIndex = ref(0)

const queryParams = ref({})

const previewWorkRef = ref()
const previewMaterialRef = ref()

const orderInfo = ref({
  taskUtensils: [],
})

const prompts = computed(() => {
  const value = []

  return value
})

onLoad((query) => {
  queryParams.value = query
})

async function getOrderInfo() {
  const res = await getMwhTask(queryParams.value.taskId)

  orderInfo.value = {
    ...res.data,
    taskUtensils: [],
  }
}

async function getMaterialList() {
  const res = await getMwhMaterialList({
    taskId: queryParams.value.taskId,
  })

  orderInfo.value.taskUtensils = res.data
}

watchEffect(() => {
  if (!queryParams.value.taskId) {
    return false
  }

  if (tabIndex.value === 1) {
    getMaterialList()
  }
  else {
    getOrderInfo()
  }
})
</script>

<template>
  <app-layout :prompts="prompts">
    <template #content-top>
      <app-tabs v-model="tabIndex" :model="tabModel"></app-tabs>
    </template>

    <view class="size-full mt-1">
      <InfoOrder v-if="tabIndex === 0" ref="previewWorkRef" :order-info="orderInfo" />
      <InfoMaterial v-else-if="tabIndex === 1" ref="previewMaterialRef" :order-info="orderInfo" />
    </view>
  </app-layout>
</template>

<style>
</style>
