<script setup>
import { postMwhPicIdentify } from '@/api/warehouse/index.js'
import { setStoreQuery } from '@/utils'
import { batchUpload } from '@/utils/upload/index.js'

const fileList = ref([])

const fileIndex = ref(0)

const { scrollLeft } = useScrollView({
  scrollIndex: fileIndex,
  scrollDistance: 50,
})

const prompts = computed(() => {
  const value = []

  if (fileList.value.length) {
    value.push({
      label: '识别',
      action: () => {
        onSubmitClick()
      },
    })
    value.push({
      label: '删除',
      action: () => {
        onRemoveClick()
      },
    })
  }

  return value
})

async function onTakeClick() {
  try {
    const ret = await uni.chooseImage({
      count: 9,
      sizeType: ['original'],
      sourceType: ['camera'],
      // sizeType: ['original', 'compressed'],
      // sourceType: ['camera', 'album'],
    })

    const remoteFilePaths = await batchUpload(ret.tempFilePaths, { showProgress: true })

    fileList.value.push(...remoteFilePaths)

    fileIndex.value = fileList.value.length - 1
  }
  catch (error) {
    console.warn(error.message || '拍照失败')
  }
  finally {
    uni.hideLoading()
  }
}

function onImageClick(item, index) {
  fileIndex.value = index
}

async function onRemoveClick() {
  try {
    const ret = await uni.showModal({
      title: '提示',
      content: '确定要删除该图片吗?',
    })

    if (ret.cancel) {
      throw new Error('用户已取消')
    }
  }
  catch (error) {
    return false
  }

  fileList.value = fileList.value.filter((item, index) => index !== fileIndex.value)

  await nextTick()

  if (fileList.value[fileIndex.value - 1]) {
    --fileIndex.value
  }
}

async function onSubmitClick() {
  uni.showLoading({
    title: '识别中',
  })
  try {
    const res = await postMwhPicIdentify({
      urls: fileList.value.join(','),
      bizType: 1,
    })

    const resultId = setStoreQuery(res.data)

    uni.navigateTo({
      url: `/pages/warehouse/task/create-confirm?resultId=${resultId}`,
    })
  }
  finally {
    uni.hideLoading()
  }
}
</script>

<template>
  <app-layout :prompts="prompts">
    <view class="size-full">
      <app-content :border="!fileList.length">
        <view v-if="!fileList.length" class="flex flex-col items-center justify-center size-full" aria-label="拍照" @click="onTakeClick">
          <view class="absolute top-2 inset-center-x text-sm">
            新建料库任务
          </view>

          <image
            src="~@/assets/images/railway/steps/icon-camera.png"
            mode="aspectFit"
            class="size-6"
          />

          <view class="text-xs text-[#9BA6C1] mt-1">
            拍照
          </view>
        </view>

        <view v-else class="size-full flex flex-col overflow-hidden p-2">
          <view class="flex-1 h-0 relative">
            <image
              :src="fileList[fileIndex]"
              mode="aspectFit"
              class="size-full"
            />

            <view class="absolute top-0 left-0 text-2xs">
              图 1
            </view>

            <view aria-label="删除" class="absolute top-0 right-0 bg-white/10 size-3 flex items-center justify-center rounded-full" @click="onRemoveClick()">
              <view class="i-carbon-close-large size-2  rounded-full  flex-none"></view>
            </view>
          </view>
          <scroll-view class="flex-none mt-2" scroll-x scroll-with-animation :scroll-left="scrollLeft">
            <view class="h-7 space-x-1 flex justify-center">
              <view class="flex-none h-full aspect-square bg-primary-700 flex items-center justify-center text-2xs rounded-sm overflow-hidden" @click="onTakeClick">
                拍照
              </view>

              <view v-for="(item, index) of fileList" :key="index" class="relative flex-none h-full aspect-square bg-gray-900 flex items-center justify-center rounded-sm overflow-hidden p-px border" :class="fileIndex === index ? 'border-primary-500' : 'border-transparent'" @click="onImageClick(item, index)">
                <image
                  :key="item"
                  :src="item"
                  mode="aspectFit"
                  class="size-full"
                />

                <view class="absolute inset-0 size-full bg-black/70 text-2xs flex items-center justify-center">
                  图{{ index + 1 }}
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </app-content>
    </view>
  </app-layout>
</template>

<style lang="postcss" scoped>
</style>
