<script setup>
import { omit } from 'es-toolkit'
import { postMwhTaskAdd } from '@/api/warehouse/index.js'
import { getStoreQuery } from '@/utils'
import PreviewMaterial from './components/preview-material/index.vue'
import PreviewWork from './components/preview-work/index.vue'

const tabModel = [
  {
    label: '工单信息',
  },
  {
    label: '物料信息',
  },
]

const tabIndex = ref(0)

const queryParams = ref({})

const previewWorkRef = ref()
const previewMaterialRef = ref()

const orderInfo = ref({})

const prompts = computed(() => {
  const value = []

  value.push({
    label: '提交',
    action: () => {
      onSubmitClick()
    },
  })

  return value
})

onLoad((query) => {
  queryParams.value = query

  orderInfo.value = getStoreQuery(query.resultId, false)
})

async function onSubmitClick() {
  const params = omit(orderInfo.value, ['id'])

  const res = await postMwhTaskAdd(params)

  uni.showToast({
    icon: 'none',
    title: res.msg || '创建料库任务成功',
  })

  const taskId = res.data.id

  uni.navigateTo({
    url: `/pages/warehouse/task/receive?taskId=${taskId}`,
  })
}
</script>

<template>
  <app-layout :prompts="prompts">
    <template #content-top>
      <app-tabs v-model="tabIndex" :model="tabModel"></app-tabs>
    </template>

    <view class="size-full mt-1">
      <PreviewWork v-if="tabIndex === 0" ref="previewWorkRef" :order-info="orderInfo" />
      <PreviewMaterial v-else-if="tabIndex === 1" ref="previewMaterialRef" :order-info="orderInfo" />
    </view>
  </app-layout>
</template>

<style>
</style>
