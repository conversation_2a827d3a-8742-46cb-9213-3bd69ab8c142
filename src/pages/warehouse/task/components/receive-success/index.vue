<script setup>
const visible = ref(false)
const diffList = ref([])

function open(data) {
  visible.value = true
  diffList.value = data
}

const diffNames = computed(() => {
  const value = diffList.value.map(item => item.utensilName)

  return [...new Set(value)].join('、')
})

defineExpose({
  open,
})
</script>

<template>
  <view v-if="visible" class="absolute inset-0 bg-[#121419] size-full z-50 flex flex-col items-center justify-center space-y-3">
    <view class="flex items-center space-x-2 text-[#28E6B6]">
      <view class="i-carbon-checkmark-filled size-[36rpx]"></view>
      <view class="">
        领取成功
      </view>
    </view>

    <view v-if="diffList.length" class="text-2xs text-gray-300">
      其中【{{ diffNames }}】与物料单不一致
    </view>
  </view>
</template>

<style>
</style>
