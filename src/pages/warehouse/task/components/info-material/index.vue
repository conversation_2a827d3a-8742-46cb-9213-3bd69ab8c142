<script setup>
const props = defineProps({
  orderInfo: {
    type: Object,
    default: () => ({}),
  },
})

const materialList = computed(() => {
  return props.orderInfo.taskUtensils || []
})
</script>

<template>
  <view class="size-full flex flex-col bg-[#121419]">
    <view class="flex-1 h-0 overflow-hidden mt-1">
      <wd-table :data="materialList" height="100%" :border="false" custom-class="wd-table--designer">
        <wd-table-col prop="utensilName" label="工具" width="25%">
          <template #value="{ row }">
            <wht-race-lamp v-if="row.utensilName?.length > 10" :speed="30">
              {{ row.utensilName || '-' }}
            </wht-race-lamp>
            <view v-else class="truncate">
              {{ row.utensilName || '-' }}
            </view>
          </template>
        </wd-table-col>

        <wd-table-col prop="position" label="位置" width="30%">
          <template #value="{ row }">
            <wht-race-lamp v-if="row.position?.length > 10" :speed="30">
              {{ row.position || '-' }}
            </wht-race-lamp>
            <view v-else class="truncate">
              {{ row.position || '-' }}
            </view>
          </template>
        </wd-table-col>

        <wd-table-col prop="utensilCount" label="计划数" width="15%" align="left">
          <template #value="{ row }">
            <view class="truncate">
              {{ row.utensilCount || '-' }}
            </view>
          </template>
        </wd-table-col>

        <wd-table-col prop="receiveCount" label="领取数" width="15%" align="left">
          <template #value="{ row }">
            <view class="truncate">
              {{ row.receiveCount || '-' }}
            </view>
          </template>
        </wd-table-col>

        <wd-table-col prop="returnCount" label="归还数" width="15%" align="left">
          <template #value="{ row }">
            <view class="truncate">
              {{ row.returnCount || '-' }}
            </view>
          </template>
        </wd-table-col>
      </wd-table>
    </view>
  </view>
</template>

<style>
</style>
