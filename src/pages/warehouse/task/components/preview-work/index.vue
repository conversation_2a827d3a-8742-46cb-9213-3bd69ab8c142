<script setup>
const props = defineProps({
  orderInfo: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<template>
  <view class="size-full pb-1 flex flex-col overflow-hidden bg-[#121419]">
    <view class="flex-1 h-0 overflow-hidden mt-1">
      <scroll-view class="size-full" scroll-y>
        <view class="bg-[#282e3b] rounded overflow-hidden">
          <view class="space-y-1 relative p-2 border-b border-dashed border-[#6D788D]">
            <view class="absolute bottom-0 inset-x-0 flex justify-between items-center translate-y-1/2">
              <view class="size-3 bg-[#121419] rounded-full -translate-x-1/2"></view>
              <view class="size-3 bg-[#121419] rounded-full translate-x-1/2"></view>
            </view>

            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                工区:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.workArea || '-' }}
              </view>
            </view>

            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                作业票号:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.workTicketNum || '-' }}
              </view>
            </view>

            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                施工日计划编号:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.constructionDayPlanNum || '-' }}
              </view>
            </view>

            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                工作票有效期:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.workTicketValidityPeriod || '-' }}
              </view>
            </view>
          </view>

          <view class="space-y-1 relative p-2 border-b border-dashed border-[#6D788D]">
            <view class="absolute bottom-0 inset-x-0 flex justify-between items-center translate-y-1/2">
              <view class="size-3 bg-[#121419] rounded-full -translate-x-1/2"></view>
              <view class="size-3 bg-[#121419] rounded-full translate-x-1/2"></view>
            </view>

            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                工作领导人:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.workLeader || '-' }}
              </view>
            </view>
            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                驻站联络人:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.stationLiaisonPerson || '-' }}
              </view>
            </view>
            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                地线监护人:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.groundLineGuardian || '-' }}
              </view>
            </view>
            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                工作组员:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.workTeam || '-' }}
              </view>
            </view>
          </view>

          <view class="space-y-1 relative p-2">
            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                所属站点:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.stationName || '-' }}
              </view>
            </view>
            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                所属库房:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.warehouseName || '-' }}
              </view>
            </view>
            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                工单类型:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.workOrderType || '-' }}
              </view>
            </view>
            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                任务类型:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.taskType || '-' }}
              </view>
            </view>
            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                责任人:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.responsiblePerson || '-' }}
              </view>
            </view>
            <view class="flex items-start text-xs space-x-2">
              <view class="flex-none w-28 text-[#9BA6C1] truncate text-right">
                备注:
              </view>
              <view class="flex-1 w-0">
                {{ orderInfo.remark || '-' }}
              </view>
            </view>
          </view>
        </view>

        <view class="h-2"></view>
      </scroll-view>
    </view>
  </view>
</template>

<style>
</style>
