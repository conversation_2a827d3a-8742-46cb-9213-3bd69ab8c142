<script setup>
import { doubleFormatter } from '@/utils/index.js'

const props = defineProps({
  orderInfo: {
    type: Object,
    default: () => ({}),
  },
})

const tts = useXfyunTTS()

const materialList = computed(() => {
  return props.orderInfo.taskUtensils || []
})

onMounted(() => {
  materialList.value.forEach((item) => {
    const content = `${item.utensilName}， 位置:${item.position}`
    tts.speak(content)
  })
})
</script>

<template>
  <view class="size-full flex flex-col bg-[#121419]">
    <view class="flex-1 h-0 overflow-hidden mt-1">
      <wd-table :data="materialList" height="100%" :border="false" custom-class="wd-table--designer">
        <wd-table-col prop="utensilName" label="工具" width="30%"></wd-table-col>
        <wd-table-col prop="utensilCount" label="计划数量" width="20%" align="center">
          <template #value="{ row }">
            {{ doubleFormatter(row.utensilName, row.utensilCount) }}
          </template>
        </wd-table-col>
        <wd-table-col prop="position" label="位置" width="30%">
          <template #value="{ row }">
            <wht-race-lamp v-if="row.position?.length > 10" :speed="30">
              {{ row.position || '-' }}
            </wht-race-lamp>
            <view v-else class="truncate">
              {{ row.position || '-' }}
            </view>
          </template>
        </wd-table-col>
        <wd-table-col prop="stockCount" label="库存数量" width="20%" align="center">
          <template #value="{ row }">
            {{ row.stockCount || 0 }}
          </template>
        </wd-table-col>
      </wd-table>
    </view>
  </view>
</template>

<style>
</style>
