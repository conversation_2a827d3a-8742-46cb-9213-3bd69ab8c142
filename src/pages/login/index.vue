<script setup>
import LoginAccount from './components/account/index.vue'
import LoginScanner from './components/scanner/index.vue'

const userStore = useUserStore()

const scanFlag = ref(true)

const accountRef = ref()

const prompts = computed(() => {
  const value = []

  if (scanFlag.value) {
    value.push({
      label: '扫描',
      action: () => {
        onScanClick()
      },
    })
    value.push({
      label: '账号登录',
      action: () => {
        scanFlag.value = false
      },
    })
  }
  else {
    value.push({
      label: '登录',
      action: () => {
        onAccountClick()
      },
    })

    value.push({
      label: '扫描登录',
      action: () => {
        scanFlag.value = true
      },
    })
  }

  return value
})

async function onScanClick() {
  try {
    const ret = await uni.scanCode({
      scanType: 'qrCode',
      onlyFromCamera: true,
      autoZoom: false,
    })

    console.log('onScanClick.ret', ret)

    onLoginClick({
      workNo: ret.result,
    })
  }
  catch (error) {
    await uni.showToast({
      icon: 'error',
      title: error.message || '扫描工作证失败',
    })
  }
}

async function onAccountClick() {
  if (!accountRef.value.workNo) {
    uni.showToast({
      icon: 'none',
      title: '工号不能为空',
    })

    return false
  }

  onLoginClick({
    workNo: accountRef.value.workNo,
  })
}

async function onLoginClick(params) {
  uni.showLoading({
    title: '正在登录',
    icon: 'none',
  })

  try {
    await userStore.login({
      ...params,
      workNo: params.workNo,
      toHome: true,
    })
  }
  catch (error) {
    await uni.showToast({
      title: error.message || '登录失败',
      duration: 2000,
      icon: 'none',
    })
  }
  finally {
    uni.hideLoading()
  }
}
</script>

<template>
  <app-layout :prompts="prompts" hidden-back-button hidden-home-button>
    <LoginScanner v-if="scanFlag" />
    <LoginAccount v-else ref="accountRef" />
  </app-layout>
</template>

<style>
</style>
