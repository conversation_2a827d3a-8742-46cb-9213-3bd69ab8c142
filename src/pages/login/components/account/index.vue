<script setup>
const workNo = ref('')

defineExpose({
  workNo,
})
</script>

<template>
  <view class="flex flex-col justify-center items-center size-full space-y-4 -mt-4">
    <view class="">
      工号录入
    </view>

    <view class="h-12 w-full px-6">
      <app-content border custom-class="">
        <input v-model="workNo" type="text" class="size-full px-4 py-1 text-center text-white" placeholder-style="color:#9BA6C1" placeholder="请输入">

        <view class="absolute top-[10rpx] left-[10rpx] size-2 border-t border-l border-[#9BA6C1] overflow-hidden rounded-tl-sm"></view>
        <view class="absolute top-[10rpx] right-[10rpx] size-2 border-t border-r border-[#9BA6C1] overflow-hidden rounded-tr-sm"></view>
        <view class="absolute bottom-[10rpx] left-[10rpx] size-2 border-b border-l border-[#9BA6C1] overflow-hidden rounded-bl-sm"></view>
        <view class="absolute bottom-[10rpx] right-[10rpx] size-2 border-b border-r border-[#9BA6C1] overflow-hidden rounded-br-sm"></view>
      </app-content>
    </view>
  </view>
</template>

<style>

</style>
