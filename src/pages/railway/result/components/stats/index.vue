<script setup>
import { getTaskStatistics } from '@/api/railway/index.js'

const userStore = useUserStore()

const statsInfo = ref({})

const abnormal = computed(() => !!statsInfo.value.exceptionEventCount)

async function getStatsInfo() {
  const res = await getTaskStatistics(
    {
      userNo: userStore.userNo,
    },
  )

  statsInfo.value = res.data
}

onMounted(() => {
  getStatsInfo()
})
</script>

<template>
  <view class="size-full py-6">
    <view class="h-full rounded bg-[#282e3b] relative">
      <image
        v-if="abnormal"
        src="~@assets/images/railway/result/bg-error.png"
        mode="scaleToFill"
        class="size-full"
      />
      <image
        v-else
        src="~@assets/images/railway/result/bg-success.png"
        mode="scaleToFill"
        class="size-full"
      />
      <view class="absolute inset-0 size-full flex items-center">
        <view class="flex flex-col items-center   flex-1 w-0 space-y-1 mt-1" :class="abnormal ? 'text-red-500' : 'text-green-500'">
          <view class="size-6" :class="abnormal ? 'i-carbon-warning-alt-filled' : 'i-carbon-checkmark-filled'">
          </view>

          <view class="text-xs font-bold">
            {{ abnormal ? '异常事项' : '完成任务' }}
          </view>
        </view>

        <view class="w-px h-1/2 bg-gray-500 flex-none"></view>

        <view class="flex flex-col items-center flex-1 w-0 space-y-1">
          <view class="text-xl font-bold">
            {{ statsInfo.stepItemCount }}
          </view>

          <view class="text-2xs text-gray-400">
            完成事项
          </view>
        </view>

        <!-- <view class="flex flex-col items-center flex-1 w-0 space-y-1">
          <view class="text-xl font-bold relative">
            32
            <view class="text-2xs absolute bottom-1 translate-x-full -right-[4rpx]">%</view>
          </view>

          <view class="text-2xs text-gray-400">完成率</view>
        </view> -->

        <view class="flex flex-col items-center flex-1 w-0 space-y-1">
          <view class="text-xl font-bold">
            {{ statsInfo.exceptionEventCount }}
          </view>

          <view class="text-2xs text-gray-400">
            异常事件
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style>
</style>
