<script setup>
import { getWorkflowListInfoByTaskId } from '@/api/railway/index.js'

const userStore = useUserStore()

const stepList = ref([])

onMounted(() => {
  getStepList()
})

async function getStepList() {
  const params = {
    userNo: userStore.userNo,
  }

  const res = await getWorkflowListInfoByTaskId(params).send(true)

  stepList.value = res.data
}
</script>

<template>
  <scroll-view class="size-full py-1" scroll-y>
    <view class="w-full space-y-1">
      <view v-for="(item, index) of stepList" :key="index" class="bg-[#242a38] rounded-sm py-1 pl-1 pr-2 w-full flex items-center">
        <view class="flex-none w-8 text-center text-xs">
          {{ index + 1 }}
        </view>

        <view class="flex-none text-xs ml-1">
          {{ item.workflowName }}
        </view>
        <view class="flex-none text-2xs text-gray-400 ml-2">
          {{ item.personChargeName }}
        </view>

        <view class="flex-none size-4 ml-auto" :class="['3'].includes(item.status) ? 'i-carbon-checkmark text-green-500' : 'i-carbon-warning-alt-filled text-red-500'"></view>
      </view>
    </view>
  </scroll-view>
</template>

<style>

</style>
