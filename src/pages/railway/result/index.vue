<script setup>
import ResultMatters from './components/matters/index.vue'
import ResultStats from './components/stats/index.vue'

const prompts = computed(() => {
  const value = []

  value.push({
    label: '完成',
    action: () => {
      uni.reLaunch({
        url: '/pages/index/index',
      })
    },
  })

  return value
})

const tabsModel = computed(() => [
  {
    label: '统计数据',
  },
  {
    label: '完成事项',
    badge: 32,
  },
])

const activeTab = ref(0)
</script>

<template>
  <app-layout :prompts="prompts">
    <view class="h-full flex flex-col">
      <view class="flex-none">
        <app-tabs v-model="activeTab" :model="tabsModel"></app-tabs>
      </view>

      <view class="flex-1 h-0 overflow-hidden">
        <ResultStats v-if="[0].includes(activeTab)" />
        <ResultMatters v-if="[1].includes(activeTab)" />
      </view>
    </view>
  </app-layout>
</template>

<style>
</style>
