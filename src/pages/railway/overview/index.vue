<script setup>
import StepsMatter from './components/matter/index.vue'
import StepsMonitor from './components/monitor/index.vue'

const showMonitor = ref(false)

const tabModel = computed(() => {
  const value = [
    {
      label: '环节事项',
      value: 'matter',
    },

  ]

  if (showMonitor.value) {
    value.push({
      label: '监护画面',
      value: 'monitor',
    })
  }

  return value
})

const tabIndex = ref('matter')

const matterRef = ref()

const prompts = computed(() => {
  const value = []

  switch (tabIndex.value) {
    case 'matter':
      break
    case 'monitor':

      break
  }

  value.push({
    label: '进入环节',
    action: () => {
      if (matterRef.value && matterRef.value.enter) {
        matterRef.value.enter()
      }
    },
  })

  return value
})

// 处理语音提示动作
function onPromptsAction(item) {
  if (item.action) {
    item.action()
  }
}

onLoad(() => {})

const monitorRef = ref()

watchEffect(() => {
  if (!monitorRef.value) {
    return false
  }

  if (tabIndex.value === 'monitor') {
    monitorRef.value.play()
  }
  else {
    monitorRef.value.stop()
  }
})
</script>

<template>
  <app-layout :prompts="prompts" @prompts-action="onPromptsAction">
    <view class="h-full flex flex-col">
      <view class="flex-none">
        <app-tabs v-model="tabIndex" :model="tabModel" />
      </view>

      <view class="flex-1 h-0 flex overflow-auto mt-1">
        <StepsMatter v-if="['matter'].includes(tabIndex)" ref="matterRef" v-model:show-monitor="showMonitor" />
        <StepsMonitor v-if="['monitor'].includes(tabIndex)" ref="monitorRef" />
      </view>
    </view>
  </app-layout>
</template>

<style>
</style>
