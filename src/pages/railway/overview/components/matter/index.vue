<script setup>
import { getWorkflowListByUserNo } from '@/api/railway/index.js'

const showMonitor = defineModel('showMonitor', {
  type: Boolean,
  default: false,
})

const userStore = useUserStore()

const stepList = ref([])

onShow(() => {
  getStepList()
})

async function getStepList() {
  const params = {
    userNo: userStore.userId,
  }

  const res = await getWorkflowListByUserNo(params).send(true)

  stepList.value = res.data?.workflowList || []

  showMonitor.value = ['leader'].includes(res.data.processRole)
}

function onItemClick(step) {
  if (!['2'].includes(step.status)) {
    uni.showToast({
      title: '只有进行中的环节可以进入',
      icon: 'none',
    })

    return false
  }

  uni.navigateTo({ url: `/pages/railway/steps/index?stepId=${step.id}` })
}

function enter() {
  const activeStep = stepList.value.find(item => ['2'].includes(item.status))

  onItemClick(activeStep)
}

defineExpose({
  enter,
})
</script>

<template>
  <view class="grid grid-cols-2 auto-rows-min gap-1 w-full">
    <view v-for="(item, index) of stepList" :key="index" :aria-label="item.workflowName" class="text-2xs h-7 flex items-center bg-[#242a38] pl-2 pr-1 py-1 truncate relative" @click="onItemClick(item, index)">
      <view v-if="['3'].includes(item.status)" class="bg-gradient-to-r from-[rgba(40,230,182,0.1)] absolute inset-0 size-full border-l-3 border-[rgba(40,230,182,1)]"></view>
      <view v-else-if="['2'].includes(item.status)" class="bg-gradient-to-r from-[rgba(58,141,247,0.2)] absolute inset-0 size-full border-l-3 border-[rgba(58,141,247,1)]"></view>
      <view v-else class="absolute inset-0 size-full border-l-3 border-[#434E64]"></view>
      <view class="relative flex items-center w-full">
        <view class="flex-none w-4 mr-1 text-left">
          {{ index + 1 }}
        </view>
        <view class="flex-1 w-0 truncate">
          {{ item.workflowName }}
        </view>
      </view>
    </view>

    <view v-if="!stepList.length" class="inset-center text-gray-500 text-xs">
      暂无数据
    </view>
  </view>
</template>

<style>
</style>
