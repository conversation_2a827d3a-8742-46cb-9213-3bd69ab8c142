<script setup>
import { Enum } from 'enum-plus'
import { getWorkflowDetailInfo, getWorkflowListInfo } from '@/api/railway/index.js'
import { roleTypeEnum } from '@/dicts/index.js'
import { generateLiveUrl } from '@/utils/index.js'

const stepStatusEnum = Enum({
  completed: '3',
  in_progress: '2',
  pending: '1',
})

const userStore = useUserStore()

const userList = ref([])
const currentUser = ref(null)
const workflowDetail = ref(null)
const loading = ref(false)
const error = ref(null)

const userIndex = ref(0)
const scrollLeft = ref(0)
const scrollDistance = 150
const livePath = ref('')
const isRenderVideo = ref(false)

// 计算属性
const currentUserInfo = computed(() => {
  if (userList.value.length > 0 && userIndex.value < userList.value.length) {
    return userList.value[userIndex.value]
  }
  return null
})

const stepList = computed(() => {
  const value = []

  if (workflowDetail.value?.currentStepName) {
    value.push({
      label: workflowDetail.value.currentStepName,
      status: stepStatusEnum.label(workflowDetail.value.status),
    })
  }

  if (workflowDetail.value?.nextStepName) {
    value.push({
      label: workflowDetail.value.nextStepName,
      status: 'pending',
    })
  }

  return value
})

// 获取工作流列表信息
async function fetchWorkflowListInfo() {
  try {
    loading.value = true
    error.value = null

    const res = await getWorkflowListInfo({
      userNo: userStore.userNo,
    }).send(true)

    userList.value = res.data.map(user => ({
      ...user,
      label: user.personChargeName,
      role: user.personChargeType,
    }))

    // 设置当前用户
    if (userList.value.length > 0) {
      currentUser.value = userList.value[0]
      fetchWorkflowDetailInfo(currentUser.value)
    }
  }
  catch (err) {
    error.value = err.message || '获取工作流列表失败'
    console.error('获取工作流列表失败:', err)
  }
  finally {
    loading.value = false
  }
}

// 获取工作流详细信息
async function fetchWorkflowDetailInfo(userParams) {
  try {
    const res = await getWorkflowDetailInfo({
      taskId: userParams.taskId,
      personChargeId: userParams.personChargeId,
      personChargeType: userParams.personChargeType,
    }).send(true)

    workflowDetail.value = res.data

    livePath.value = `/${res.data?.liveVideoUrl}`
  }
  catch (err) {
    // error.value = err.message || '获取工作流详情失败'
    console.error('获取工作流详情失败:', err)
  }
}

// 滚动和用户选择相关方法
function onScroll({ detail }) {
  scrollLeft.value = detail.scrollLeft
}

function onPrevClick() {
  userIndex.value = Math.max(0, userIndex.value - 1)
}

function onNextClick() {
  userIndex.value = Math.min(userList.value.length - 1, userIndex.value + 1)
}

function onUserClick(index) {
  userIndex.value = index

  // 当选择用户时，获取该用户相关的工作流详情
  const selectedUser = userList.value[index]
  if (selectedUser && selectedUser.personChargeId) {
    fetchWorkflowDetailInfo(selectedUser)
  }
}

// 监听用户索引变化，自动滚动
watchEffect(() => {
  scrollLeft.value = scrollDistance * (userIndex.value - 1.5)
})

// 监听当前用户变化
watch(currentUserInfo, (newUser) => {
  if (newUser) {
    currentUser.value = newUser
  }
})

// 获取步骤状态样式
function getStepStatusClass(status) {
  switch (status) {
    case 'completed':
      return 'bg-green-500'
    case 'in_progress':
      return 'bg-primary-500'
    case 'pending':
      return 'bg-gray-400'
    default:
      return 'bg-gray-400'
  }
}

// 获取步骤文本样式
function getStepTextClass(status) {
  switch (status) {
    case 'completed':
      return 'text-green-400'
    case 'in_progress':
      return 'text-primary-400'
    case 'pending':
      return 'text-gray-400'
    default:
      return 'text-gray-400'
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchWorkflowListInfo()
})

// 组件卸载时清理事件监听
onUnmounted(() => {
})

// 暴露给父组件的方法
defineExpose({
  play() {
    isRenderVideo.value = true
  },
  stop() {
    isRenderVideo.value = false
  },
  refresh() {
    fetchWorkflowListInfo()
  },
})

function onVideoError() {
  livePath.value = ''
}

function onRefreshClick() {
  fetchWorkflowDetailInfo(currentUser.value)
}
</script>

<template>
  <view class="size-full overflow-hidden flex flex-col space-y-1">
    <view class="flex-1 h-0 space-x-1 flex">
      <view class="flex-none aspect-ratio-[16/9] h-full bg-[#282e3b] overflow-hidden rounded-sm">
        <video
          v-if="isRenderVideo && livePath"
          :key="livePath"
          :src="generateLiveUrl(livePath)"
          is-live
          class="size-full"
          object-fit="cover"
          autoplay
          :aria-label="`监控视频 - ${currentUserInfo?.name || '当前用户'}`"
          @error="onVideoError"
        ></video>
        <view
          v-else
          class="size-full flex flex-col items-center justify-center text-gray-400 space-y-2"
        >
          <view class="i-carbon-video-off size-7 opacity-50"></view>
          <view class="text-center text-2xs">
            直播未开始
          </view>
          <button aria-label="刷新" class="app-button app-button--primary" @click="onRefreshClick">
            刷新
          </button>
        </view>
      </view>

      <view class="flex-1 w-0 px-2 py-2 bg-[#282e3b] overflow-hidden rounded-sm">
        <!-- 加载状态 -->
        <view v-if="loading" class="flex items-center justify-center h-full">
          <text class="text-2xs text-gray-400 ml-2">
            加载中...
          </text>
        </view>

        <!-- 错误状态 -->
        <view v-else-if="error" class="flex flex-col items-center justify-center h-full text-center text-gray-400">
          <view class="i-carbon-warning size-4 mb-2"></view>

          <button aria-label="重试" class="app-button app-button--primary" @click="fetchWorkflowListInfo">
            重试
          </button>
        </view>

        <template v-else>
          <!-- 当前用户信息 -->
          <view class="flex items-center">
            <image
              src="~@assets/images/railway/steps/icon-helmet.png"
              mode="aspectFit"
              class="size-3 mb-px"
            />

            <text class="text-2xs ml-1">
              {{ roleTypeEnum[currentUserInfo?.role] }}
            </text>
            <text class="text-2xs border-l border-dashed border-gray-600 ml-1 pl-1">
              {{ currentUserInfo?.label }}
            </text>
          </view>

          <!-- 步骤列表 -->
          <view class="mt-4 ml-1">
            <view v-for="(item, index) of stepList" :key="item.id || index" class="">
              <view class="flex space-x-2">
                <view class="flex flex-col items-center">
                  <view
                    class="size-[14rpx] rounded-full flex-none"
                    :class="getStepStatusClass(item.status)"
                  ></view>
                  <view v-if="index + 1 !== stepList.length" class="border-l border-dashed border-gray-600 h-full"></view>
                </view>

                <view
                  class="flex-1 text-2xs pb-4 -mt-[7rpx]"
                  :class="getStepTextClass(item.status)"
                >
                  {{ item.label }}
                  <text v-if="item.completedTime" class="block text-gray-500 mt-1">
                    完成时间: {{ item.completedTime }}
                  </text>
                </view>
              </view>
            </view>

            <!-- <view class="text-xs">
              {{ workflowDetail.currentStepName }}
            </view>

            <view class="text-2xs mt-1">
              {{ workflowDetail.currentStepContent }}
            </view> -->
          </view>
        </template>
      </view>
    </view>

    <view class="flex-none flex bg-[#333a4a] rounded-sm overflow-hidden h-5">
      <view
        aria-label="上翻"
        class="flex-none flex items-center pr-2 text-gray-100 hover:bg-gray-600 transition-colors focus:outline-none focus:bg-gray-600"
        :disabled="userIndex <= 0"
        :class="{ 'opacity-50 cursor-not-allowed': userIndex <= 0 }"
        @click="onPrevClick"
      >
        <view class="i-carbon-chevron-left size-3"></view>
        <text class="text-xs">
          上翻
        </text>
      </view>
      <scroll-view class="flex-1 w-0 bg-[#282e3b] h-full" scroll-x :scroll-left="scrollLeft" scroll-with-animation @scroll="onScroll">
        <view class="flex h-full">
          <view
            v-for="(item, index) of userList"
            :key="item.id || index"
            :aria-label="item.label"
            class="flex-none h-full flex items-center justify-center space-x-1 relative"
            :class="index === userIndex ? 'text-primary-500 w-16' : 'text-gray-400 w-14'"
            @click="onUserClick(index)"
          >
            <!-- 在线状态指示器 -->
            <!-- <view
              v-if="item.status === 'online'"
              class="absolute top-1 right-1 size-1 bg-green-500 rounded-full"
            ></view>
            <view
              v-else
              class="absolute top-1 right-1 size-1 bg-gray-500 rounded-full"
            ></view> -->

            <!-- 视频图标（当前选中用户） -->
            <view v-if="index === userIndex" class="i-carbon-video-chat size-3"></view>

            <!-- 用户名 -->
            <text class="text-xs leading-none truncate">
              {{ item.label }}
            </text>
          </view>

          <!-- 空状态 -->
          <view v-if="userList.length === 0" class="flex items-center justify-center h-full w-full">
            <text class="text-xs text-gray-500">
              暂无用户
            </text>
          </view>
        </view>
      </scroll-view>
      <view
        aria-label="下翻"
        class="flex-none flex items-center pl-2 text-gray-100 hover:bg-gray-600 transition-colors focus:outline-none focus:bg-gray-600"
        :disabled="userIndex >= userList.length - 1"
        :class="{ 'opacity-50 cursor-not-allowed': userIndex >= userList.length - 1 }"
        @click="onNextClick"
      >
        <text class="text-xs">
          下翻
        </text>
        <view class="i-carbon-chevron-right size-3"></view>
      </view>
    </view>

    <view class="h-1"></view>
  </view>
</template>

<style>
</style>
