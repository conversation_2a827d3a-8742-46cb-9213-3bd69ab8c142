<script setup>
const toolList = ref([])
const toolImage = ref('')

const visible = ref(false)

function open(params) {
  visible.value = true
  toolList.value = params?.data || []
  toolImage.value = params?.image
}

function close() {
  visible.value = false
}

defineExpose({
  open,
  close,
  toolList,
  toolImage,
})
</script>

<template>
  <view v-if="visible" class="absolute inset-0 size-full z-50 bg-[#121419]">
    <view class="flex size-full space-x-1 px-2 py-1">
      <view class="flex-1 w-0">
        <app-content>
          <wd-table :data="toolList" height="100%" :border="false" custom-class="wd-table--designer">
            <wd-table-col prop="utensilName" label="工具名称" width="50%"></wd-table-col>
            <wd-table-col prop="identifyCount" label="识别数量" width="50%" align="center">
              <template #value="{ row }">
                {{ row.identifyCount || 0 }}
              </template>
            </wd-table-col>
          </wd-table>
        </app-content>
      </view>

      <view class="flex-none w-36">
        <app-content custom-class="flex items-center justify-center p-2">
          <image
            :src="toolImage"
            mode="aspectFit"
            class="h-full"
          />
        </app-content>
      </view>
    </view>
  </view>
</template>

<style>
</style>
