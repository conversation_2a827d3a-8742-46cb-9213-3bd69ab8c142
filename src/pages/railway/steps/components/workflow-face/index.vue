<script setup>
import { getPersonnelInformation } from '@/api/railway/index.js'

const props = defineProps({
  stepInfo: {
    type: Object,
    default: () => ({}),
  },
})

const participantList = ref([])

async function getParticipantList() {
  const res = await getPersonnelInformation({
    stepId: props.stepInfo.id,
  }).send(true)

  participantList.value = res.data
}

onMounted(() => {
  getParticipantList()
})

function refresh() {
  getParticipantList()
}

defineExpose({
  refresh,
})
</script>

<template>
  <view class="text-2xs leading-normal grid grid-cols-2 gap-2">
    <view v-for="(item, index) of participantList" :key="index" class="flex items-center space-x-1">
      <view class="size-3 flex-none" :class="['2'].includes(item.status) ? 'i-carbon-checkmark-filled text-green-500' : 'i-carbon-warning-filled text-gray-300'"></view>
      <view class="truncate" :class="['2'].includes(item.status) ? 'text-gray-50' : 'text-gray-300'">
        {{ item.userName }}
      </view>
    </view>
  </view>
</template>

<style>
</style>
