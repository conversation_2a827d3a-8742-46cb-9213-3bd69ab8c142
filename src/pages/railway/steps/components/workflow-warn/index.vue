<script setup>
import { getEventInfo, postReportEvent } from '@/api/railway/index.js'
import { railwayDetectionEnum } from '@/dicts/index.js'

const props = defineProps({
  stepInfo: {
    type: Object,
    default: () => ({}),
  },
  tts: {
    type: Object,
    default: null,
  },
  detection: {
    type: String,
    default: '',
  },
})

const detectionResult = ref({})

async function getDetectionResult() {
  const res = await getEventInfo({
    stepId: props.stepInfo.id,
    detectionItem: props.detection,
  }).send(true)

  detectionResult.value = res.data

  if (res.data.remark) {
    tts.speak(res.data.remark)
  }
}

onMounted(() => {
  getDetectionResult()
})

function refresh() {
  getDetectionResult()
}

async function onConfirmClick() {
  const params = {
    eventId: detectionResult.value.eventId,
    status: '3',
    detectionItem: props.detection,
  }

  await postReportEvent(params)

  getDetectionResult()
}

async function onRejectClick() {
  const params = {
    eventId: detectionResult.value.id,
    status: '2',
    detectionItem: props.detection,
  }

  await postReportEvent(params)

  getDetectionResult()
}

defineExpose({
  refresh,
})
</script>

<template>
  <view class="size-full absolute inset-0 flex flex-col justify-center items-center text-center px-4 space-y-2">
    <template v-if="detectionResult?.id">
      <view class="i-carbon-warning-filled size-7 flex-none mt-px text-red-500"></view>
      <view class="text-red-500 text-xs">
        {{ detectionResult.remark || '检测到异常' }}
      </view>

      <view class="flex items-center space-x-1">
        <button v-if="['1'].includes(detectionResult.status)" class="app-button app-button--primary !text-2xs" aria-label="确定异常" @click="onConfirmClick">
          确定异常
        </button>

        <button v-if="['1'].includes(detectionResult.status)" class="app-button app-button--primary app-button--plain !text-2xs" aria-label="误报" @click="onRejectClick">
          误报
        </button>

        <button v-if="['2'].includes(detectionResult.status)" class="app-button !text-2xs" aria-label="误报">
          误报已确定
        </button>

        <button v-if="['3'].includes(detectionResult.status)" class="app-button !text-2xs" aria-label="误报">
          已确定异常
        </button>
      </view>
    </template>

    <view v-else class="text-2xs">
      请点击右侧拍照按钮进行 {{ railwayDetectionEnum[detection] }}后，获取识别结果。
    </view>
  </view>
</template>

<style>
</style>
