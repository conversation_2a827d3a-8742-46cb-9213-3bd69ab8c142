<script setup>
import { batchUpload } from '@/utils/upload/index.js'

const props = defineProps({
  validate: {
    type: Function,
    default: void 0,
  },
})

const emit = defineEmits(['success'])

const recorderManager = uni.getRecorderManager()
const innerAudioContext = uni.createInnerAudioContext()

const recording = ref(false)

const fileList = defineModel('fileList', {
  type: Array,
  default: () => [],
})

recorderManager?.onStop?.(async (ret) => {
  uni.showLoading({
    title: '上传中',
  })

  try {
    const remoteFilePaths = await batchUpload([ret.tempFilePath])

    fileList.value = [
      ...fileList.value,
      ...remoteFilePaths,
    ]

    emit('success', fileList.value)
  }
  finally {
    uni.hideLoading()
  }
})

function onRecordClick() {
  recording.value = !recording.value

  if (recording.value) {
    recorderManager?.start?.()
    return false
  }

  recorderManager?.stop?.()
}

function onRemoveClick(index) {
  fileList.value = fileList.value.filter((_, index_1) => index !== index_1)
}

const playPath = ref('')

function onPlayClick(path) {
  if (playPath.value === path) {
    playPath.value = void 0

    if (innerAudioContext) {
      innerAudioContext.src = void 0
      innerAudioContext.stop()
    }

    return false
  }

  playPath.value = path

  if (innerAudioContext) {
    innerAudioContext.src = path
    console.log('innerAudioContext.src', innerAudioContext.src)
    innerAudioContext.play()
  }
}

defineExpose({
  fileList,
})
</script>

<template>
  <view
    class="size-full p-1 flex flex-col items-center  relative" :class="[
      fileList.length && !recording ? 'justify-start' : 'justify-center',
    ]"
  >
    <view
      v-if="recording"
      class="text-[#9BA6C1] text-center text-xs flex-none absolute top-2 inset-x-0"
      :class="{
        'animate-pulse': recording,
      }"
    >
      录音中
    </view>

    <view
      class="relative my-1 flex-none transition-all"
      :class="[
        fileList.length && !recording ? 'size-10' : 'size-14',
      ]"
      @click="onRecordClick"
    >
      <view
        class="absolute inset-0 bg-[rgba(79,211,255,0.1)] rounded-full backdrop-blur-xl"
        :class="{
          'animate-recording': recording,
        }"
      ></view>

      <image
        src="~@/assets/images/railway/steps/icon-voice.png"
        mode="aspectFit"
        class="size-full relative"
        :class="{
          'animate-recording__button': recording,
        }"
      />
    </view>

    <view
      class="flex-none text-xs"
      :aria-label="recording ? '停止' : '录音'"
      @click="onRecordClick"
    >
      {{ recording ? '停止' : '录音' }}
    </view>

    <scroll-view v-if="fileList.length && !recording" class="w-full  mt-2 flex-1 h-0" scroll-y>
      <view class="space-y-1">
        <view v-for="(item, index) in fileList" :key="index" class="flex items-center  py-1 px-2 w-full bg-[#1c2029] rounded-sm">
          <view class="flex-1 w-0 truncate text-2xs" :class="item === playPath ? 'animate-pulse' : ''" @click="onPlayClick(item)">
            {{ item === playPath ? '播放中' : `录音${index + 1}` }}
          </view>

          <view class="bg-white/10 size-3 flex items-center justify-center rounded-full" @click="onRemoveClick(index)">
            <view class="i-carbon-close-large size-2  rounded-full  flex-none"></view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="postcss" scoped>
@keyframes animate-recording-keyframes {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes animate-recording__button-keyframes {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-recording {
  animation: animate-recording-keyframes 2s ease-out infinite;
}

.animate-recording__button {
  animation: animate-recording__button-keyframes 2s ease-out infinite;
}
</style>
