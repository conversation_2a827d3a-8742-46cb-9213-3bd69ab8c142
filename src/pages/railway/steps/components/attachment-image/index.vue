<script setup>
import { numberToChinese } from '@/utils/index.js'
import { batchUpload } from '@/utils/upload/index.js'

const props = defineProps({
  detection: {
    type: String,
    default: '',
  },
  stepInfo: {
    type: Object,
    default: () => ({}),
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  validate: {
    type: Function,
    default: void 0,
  },
})

const emit = defineEmits(['success'])

const fileList = defineModel('fileList', {
  type: Array,
  default: () => [],
})

const detectionFileList = computed(() => {
  if (!props.detection || props.detection === 'default') {
    return fileList.value
  }

  const value = fileList.value.filter(item => item.includes(`detection=${props.detection}`))

  return value
})

const fileIndex = ref(0)

const { scrollLeft } = useScrollView({
  scrollIndex: fileIndex,
  scrollDistance: 50,
})

async function onTakeClick() {
  if (['hang_grounding_pole'].includes(props.detection)) {
    if (fileList.value.every(item => !item.includes('detection=voltage_detection'))) {
      uni.showToast({
        title: '请先进行验电检测',
        icon: 'none',
      })
      return false
    }
  }

  try {
    const ret = await uni.chooseImage({
      count: 1,
      sizeType: ['original'],
      sourceType: ['camera'],
      // sizeType: ['original', 'compressed'],
      // sourceType: ['camera', 'album'],
    })

    uni.showLoading({
      title: '上传中',
    })

    const remoteFilePaths = await batchUpload(ret.tempFilePaths)

    const resolveFilePaths = remoteFilePaths.map(item => `${item}?detection=${props.detection}`)

    fileList.value.push(...resolveFilePaths)

    fileIndex.value = detectionFileList.value.length - 1

    emit('success', fileList.value)
  }
  catch (error) {
    console.warn(error.message || '拍照失败')
  }
  finally {
    uni.hideLoading()
  }
}

function onImageClick(item, index) {
  fileIndex.value = index
}

async function onRemoveClick() {
  try {
    const ret = await uni.showModal({
      title: '提示',
      content: '确定要删除该图片吗?',
    })

    if (ret.cancel) {
      throw new Error('用户已取消')
    }
  }
  catch (error) {
    return false
  }

  fileList.value = fileList.value.filter(item => item !== detectionFileList.value[fileIndex.value])

  await nextTick()

  if (fileList.value[fileIndex.value - 1]) {
    --fileIndex.value
  }

  emit('success')
}

defineExpose({
  fileList,
})
</script>

<template>
  <view
    class="size-full p-1 relative"
  >
    <view v-if="!detectionFileList.length" class="flex flex-col items-center justify-center size-full" aria-label="拍照" @click="onTakeClick">
      <image
        src="~@/assets/images/railway/steps/icon-camera.png"
        mode="aspectFit"
        class="size-6"
      />

      <view class="text-xs text-[#9BA6C1] mt-1">
        拍照
      </view>
    </view>

    <view v-else class="size-full flex flex-col overflow-hidden">
      <view class="flex-1 h-0 relative pb-1 pt-4">
        <image
          :src="detectionFileList[fileIndex]"
          mode="aspectFit"
          class="size-full"
        />

        <view class="absolute top-0 left-0 text-2xs">
          图 1
        </view>

        <view class="absolute top-0 right-0 bg-white/10 size-3 flex items-center justify-center rounded-full" @click="onRemoveClick()">
          <view class="i-carbon-close-large size-2  rounded-full  flex-none"></view>
        </view>
      </view>
      <scroll-view class="flex-none" scroll-x scroll-with-animation :scroll-left="scrollLeft">
        <view class="h-7 space-x-1 flex">
          <view v-if="multiple" aria-label="拍照" class="flex-none h-full aspect-square bg-primary-700 flex items-center justify-center text-2xs rounded-sm overflow-hidden" @click="onTakeClick">
            拍照
          </view>

          <view v-for="(item, index) of detectionFileList" :key="index" class="relative flex-none h-full aspect-square bg-gray-900 flex items-center justify-center rounded-sm overflow-hidden p-px border" :class="fileIndex === index ? 'border-primary-500' : 'border-transparent'" :aria-label="`图${numberToChinese(index + 1)}`" @click="onImageClick(item, index)">
            <image
              :key="item"
              :src="item"
              mode="aspectFit"
              class="size-full"
            />

            <view class="absolute inset-0 size-full bg-black/70 text-2xs flex items-center justify-center">
              图{{ index + 1 }}
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<style lang="postcss" scoped>
</style>
