<script setup>
import { getWorkflowListByUserNo } from '@/api/railway/index.js'

const props = defineProps({
  stepId: {
    type: [String, Number],
    default: '',
  },
})

const userStore = useUserStore()

const stepList = ref([])

onMounted(() => {
  getStepList()
})

async function getStepList() {
  const params = {
    userNo: userStore.userId,
  }

  const res = await getWorkflowListByUserNo(params)

  stepList.value = res.data.workflowList || []
}

const prevStep = ref()
const activeStep = ref()
const nextStep = ref()

watchEffect(() => {
  if (!props.stepId || !stepList.value.length) {
    return false
  }

  const activeIndex = stepList.value.findIndex(item => item.id === props.stepId)

  if (activeIndex === -1) {
    return false
  }

  prevStep.value = stepList.value[activeIndex - 1]
  activeStep.value = stepList.value[activeIndex]
  nextStep.value = stepList.value[activeIndex + 1]
})
</script>

<template>
  <view class="flex items-center justify-center text-2xs h-full space-x-1">
    <template v-if="prevStep">
      <view class="flex-none whitespace-nowrap max-w-12 overflow-hidden">
        {{ prevStep.workflowName }}
      </view>

      <image
        src="~@assets/images/railway/steps/icon-right.png"
        mode="aspectFit"
        class="size-2 flex-none"
      />
    </template>

    <wht-race-lamp v-if="activeStep?.workflowName?.length > 5" :speed="30">
      <view class="text-primary-500">
        {{ activeStep?.workflowName }}
      </view>
      <view class="w-1">
      </view>
    </wht-race-lamp>
    <view v-else class="flex-none whitespace-nowrap text-primary-500">
      {{ activeStep?.workflowName }}
    </view>

    <template v-if="nextStep">
      <image
        src="~@assets/images/railway/steps/icon-right.png"
        mode="aspectFit"
        class="size-2 flex-none"
      />

      <view class="flex-none whitespace-nowrap max-w-12 overflow-hidden">
        {{ nextStep.workflowName }}
      </view>
    </template>
  </view>
</template>

<style>

</style>
