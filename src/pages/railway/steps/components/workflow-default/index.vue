<script setup>
const props = defineProps({
  stepInfo: {
    type: Object,
    default: () => ({}),
  },
})

function refresh() {
}

defineExpose({
  refresh,
})
</script>

<template>
  <view class="">
    <!-- <view v-if="['2'].includes(stepInfo.preStepStatus)" class="flex-none text-orange-500 flex items-center justify-center text-2xs space-x-[4rpx] pb-1">
      <view class="i-carbon-time size-[22rpx]"></view>
      <view>{{ stepInfo.tip || railwayStepPreStatusEnum[stepInfo.preStepStatus] }}</view>
    </view> -->

    <view class="text-2xs leading-normal">
      {{ stepInfo.workflowInfo }}
    </view>
  </view>
</template>

<style>
</style>
