<script setup>
import { getUtensilInfoListByWorkflowId, postSaveUtensilInfo } from '@/api/railway/index.js'

const props = defineProps({
  stepInfo: {
    type: Object,
    default: () => ({}),
  },
  detection: {
    type: String,
    default: '',
  },
})

const detectionResult = ref([])

const tableWidthList = computed(() => {
  if (['lower_utensil'].includes(props.detection)) {
   return ['30%', '25%', '25%', '20%']
  }

  return ['50%', '50%']
})

async function getDetectionResult() {
  const res = await getUtensilInfoListByWorkflowId({
    stepId: props.stepInfo.id,
  }).send(true)

  detectionResult.value = res.data
}

onMounted(() => {
  getDetectionResult()
})

function refresh() {
  getDetectionResult()
}

async function save({ utensilInfoList }) {
  const params = {
    taskId: props.stepInfo.taskId,
    stepId: props.stepInfo.id,
    type: props.detection,
    utensilInfoList,
  }

  await postSaveUtensilInfo(params)

  getDetectionResult()
}

defineExpose({
  refresh,
  save,
})
</script>

<template>
  <view v-if="detectionResult.length">
    <wd-table :data="detectionResult" :border="false" custom-class="wd-table--designer">
      <wd-table-col prop="utensilName" label="工具" :width="tableWidthList[0]">
        <template #value="{ row }">
          <wht-race-lamp v-if="row.utensilName?.length > 5" :speed="30">
            <view class="">
              {{ row.utensilName }}
            </view>
            <view class="w-1">
            </view>
          </wht-race-lamp>
          <view v-else class="flex-none whitespace-nowrap">
            {{ row.utensilName }}
          </view>
        </template>
      </wd-table-col>

      <wd-table-col v-if="['upper_utensil', 'lower_utensil'].includes(detection)" prop="upperTrackNum" label="上道数" :width="tableWidthList[1]" align="center">
        <template #value="{ row }">
          {{ row.upperTrackNum || 0 }}
        </template>
      </wd-table-col>

      <wd-table-col v-if="['lower_utensil'].includes(detection)" prop="lowerTrackNum" label="下道数" :width="tableWidthList[2]" align="center">
        <template #value="{ row }">
          {{ row.lowerTrackNum || 0 }}
        </template>
      </wd-table-col>

      <wd-table-col v-if="['lower_utensil'].includes(detection)" prop="status" label="状态" :width="tableWidthList[3]" align="center">
        <template #value="{ row }">
          <view class="flex items-center space-x-1">
            <view class="size-[24rpx] flex-none" :class="['1'].includes(row.status) ? 'i-carbon-checkmark-outline text-green-500' : 'i-carbon-close-outline text-red-500'"></view>
          </view>
        </template>
      </wd-table-col>
    </wd-table>
  </view>

  <view v-else class="size-full absolute inset-0 flex flex-col justify-center items-center text-center px-4">
    <view class="text-2xs">
      请点击右侧拍照按钮进行工器具识别后，获取识别结果。
    </view>
  </view>
</template>

<style>
</style>
