import { postStartWorkflowStep } from '@/api/railway/index.js'
import { sleep } from '@/utils/index.js'

export function useStepInfo({ params }) {
  const userStore = useUserStore()

  const stepInfo = ref({})
  const fileList = ref([])

  async function run(innerParams = params.value) {
    const res = await postStartWorkflowStep({
      ...innerParams,
      stepId: innerParams.stepId,
      arGlassId: userStore.deviceNo,
    })

    stepInfo.value = res.data

    if (stepInfo.value.annexUrl) {
      fileList.value = stepInfo.value.annexUrl.split(',')
    }

    if (['2'].includes(stepInfo.value.preStepStatus)) {
      await sleep(5 * 1000)
      run()
    }
  }

  return {
    run,
    stepInfo,
    fileList,
  }
}
