import { postStartWorkflowStep, postSubmitWorkflowStepAttach } from '@/api/railway/index.js'

export function navigateStep(stepInfo, options = {}) {
  const stepId = stepInfo.id || ''
  const navigateType = options.redirect ? 'redirectTo' : 'navigateTo'

  if (options.showTips) {
    uni.showToast({
      title: '提交成功，已进入自动下一个环节',
      icon: 'none',
    })
  }

  uni[navigateType]({
    url: `/pages/railway/steps/index?stepId=${stepId}`,
  })
}

export function useStepSubmit({ stepInfo, fileList, tts }) {
  const userStore = useUserStore()

  function validate() {
    if (['2'].includes(stepInfo.value.preStepStatus)) {
      throw new Error(stepInfo.value.tip || '当前状态不允许操作')
    }
  }

  async function run(params = {}) {
    try {
      await validate()
    }
    catch (error) {
      uni.showToast({
        icon: 'none',
        title: error.message,
      })
      return false
    }

    await nextTick()

    // if (!fileList.value.length) {
    //   uni.showToast({
    //     title: '请先上传附件',
    //     icon: 'none',
    //   })

    //   return false
    // }

    const annexUrl = (fileList.value || []).join(',')

    const res = await postSubmitWorkflowStepAttach({
      stepId: stepInfo.value.id,
      annexUrl,
      ...params,
    })

    if (params.saveType !== '2') {
      return false
    }
    
    if (res.data.status === '2') {
      const remark = res.data?.remark || '存在异常，确定要强制提交吗?'

      if (tts) {
        tts.speak(remark)
      }

      const result = await uni.showModal({
        title: '提示',
        content: remark,
      })

      if (result.confirm) {
        run({
          ...params,
          status: '2',
        })
      }

      return false
    }

    const nextStepId = res.data.nextStepId

    if (nextStepId === -1) {
      uni.redirectTo({
        url: `/pages/railway/result/index?stepId=${stepInfo.value.id}`,
      })
      return false
    }

    const nextRes = await postStartWorkflowStep({
      stepId: nextStepId,
      arGlassId: userStore.deviceNo,
    })

    navigateStep(nextRes?.data, {
      redirect: true,
      showTips: true,
    })
  }

  return {
    validate,
    run,
  }
}
