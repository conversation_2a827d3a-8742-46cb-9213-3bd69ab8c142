<script setup>
import { postCallModelByScene } from '@/api/railway/index.js'
import { railwayDetectionEnum, railwayStepPreStatusEnum } from '@/dicts/index.js'
import AttachmentAudio from './components/attachment-audio/index.vue'
import AttachmentImage from './components/attachment-image/index.vue'
import FlowProgress from './components/flow-progress/index.vue'
import WorkflowDefault from './components/workflow-default/index.vue'
import WorkflowFace from './components/workflow-face/index.vue'
import WorkflowToolPreview from './components/workflow-tool-preview/index.vue'
import WorkflowTool from './components/workflow-tool/index.vue'
import WorkflowWarn from './components/workflow-warn/index.vue'
import { useStepInfo } from './hooks/useStepInfo.js'
import { useStepSubmit } from './hooks/useStepSubmit.js'

const queryParams = ref({})

const currentStack = ref('')

const prompts = computed(() => {
  const value = []

  if (['previewing'].includes(currentStack.value)) {
    value.push({
      label: '确定',
      action: () => {
        workflowToolPreviewRef.value.close()
        currentStack.value = ''

        const params = {
          utensilInfoList: workflowToolPreviewRef.value.toolList.map(item => ({
            imageUrl: workflowToolPreviewRef.value.toolImage,
            ...item,
          })),
        }

        workflowRef.value.save(params)

        workflowRef.value?.refresh?.()
      },
    })

    value.push({
      label: '取消',
      action: () => {
        onPreviewingCancel()
      },
    })
  }
  else {
    value.push({
      label: '提交',
      action: () => {
        stepSubmitRun({ saveType: '2' })
      },
    })

    value.push({
      label: '监护画面',
      action: () => {
        uni.navigateTo({
          url: `/pages/common/live-pusher/index?path=/${stepInfo.value.taskId}/${stepInfo.value.arGlassId}/${stepInfo.value.personChargeRoleType}/${stepInfo.value.personChargeId}`,
        })
      },
    })
  }

  return value
})

const attachmentRef = ref()

const workflowRef = ref()

const workflowToolPreviewRef = ref()

const { fileList, stepInfo, run: getStepInfo } = useStepInfo({ params: queryParams })

const tts = useXfyunTTS()

const detectionList = computed(() => {
  if (!stepInfo.value.detectionItem) {
    return []
  }

  const value = stepInfo.value.detectionItem.split(',')

  return value
})

const tabsModel = computed(() => {
  const value = detectionList.value.map(item => ({
    label: `${railwayDetectionEnum[item]}`,
    value: item,
    badge: 1,
  }))

  value.unshift({
    label: (stepInfo.value?.workflowName || '').length > 5 ? '任务信息' : stepInfo.value?.workflowName,
    value: 'default',
  })

  return value
})

const activeTab = ref('default')

const { validate, run: stepSubmitRun } = useStepSubmit({
  attachmentRef,
  stepInfo,
  fileList,
  tts,
})

onLoad((query) => {
  queryParams.value = query
  getStepInfo()
})

async function handleDetection(imageUrl) {
  uni.showLoading({
    title: '识别中',
  })

  try {
    const res = await postCallModelByScene({
      imageUrl,
      stepId: queryParams.value.stepId,
      detectionItem: activeTab.value,
    })

    if (res.data?.tip && ['2'].includes(res.data?.status)) {
      tts.speak(res.data.tip, { toast: true })
    }

    if (['upper_utensil', 'lower_utensil'].includes(activeTab.value)) {
      const resultData = res.data.resultData

      if (!resultData?.length) {
        return false
      }

      currentStack.value = 'previewing'

      workflowToolPreviewRef.value.open({
        image: imageUrl,
        data: resultData,
      })
      return false
    }
  }
  finally {
    uni.hideLoading()
    workflowRef.value?.refresh?.()
  }
}

async function submit() {
  await nextTick()

  stepSubmitRun({
    saveType: '1',
  })

  const lastFile = fileList.value?.[fileList.value?.length - 1]

  if (lastFile && activeTab.value !== 'default') {
    handleDetection(lastFile)
  }
}

function onBackClick() {
  if (['previewing'].includes(currentStack.value)) {
    onPreviewingCancel()
    return false
  }

  uni.navigateBack()
}

function onPreviewingCancel() {
  fileList.value = fileList.value.filter(item => workflowToolPreviewRef.value.toolImage !== item)
  stepSubmitRun({
    saveType: '1',
  })
  workflowToolPreviewRef.value.close()
  currentStack.value = ''
}
</script>

<template>
  <app-layout :prompts="prompts" @back-click="onBackClick">
    <template #header-center>
      <FlowProgress :step-id="queryParams.stepId" />
    </template>

    <view class="flex space-x-1 h-full">
      <view class="flex-1 w-0 h-full">
        <app-content>
          <view class="flex flex-col h-full">
            <view class="flex items-center h-6 px-2 bg-[#333a4a] flex-none">
              <template v-if="detectionList.length">
                <app-tabs v-model="activeTab" :model="tabsModel" custom-class="w-full"></app-tabs>
              </template>

              <view v-else class="flex items-center space-x-1">
                <view class="app-marker"></view>
                <view class="text-xs">
                  {{ tabsModel?.[0]?.label }}
                </view>
              </view>
            </view>

            <scroll-view scroll-y class="flex-1 h-0">
              <view class="py-2 px-2 relative h-full">
                <WorkflowFace v-if="['face'].includes(activeTab)" ref="workflowRef" :step-info="stepInfo" />
                <WorkflowTool v-else-if="['upper_utensil', 'lower_utensil'].includes(activeTab)" ref="workflowRef" :step-info="stepInfo" :detection="activeTab" />
                <WorkflowWarn
                  v-else-if="[
                    'helmet_workwear',
                    'insulating_gloves_boots',
                    'grounding_wire',
                    'voltage_detection',
                    'hang_grounding_pole',
                  ].includes(activeTab)"
                  ref="workflowRef"
                  v-bind="{
                    tts,
                    stepInfo,
                    detection: activeTab,
                  }"
                />
                <WorkflowDefault v-else ref="workflowRef" :step-info="stepInfo" />
              </view>
            </scroll-view>
          </view>
        </app-content>
      </view>

      <view class="flex-none w-36">
        <app-content>
          <AttachmentImage v-if="['1'].includes(stepInfo.annexType)" :key="activeTab" ref="attachmentRef" v-model:file-list="fileList" :step-info="stepInfo" :multiple="stepInfo.isMultipleAttachments === '1' || !!detectionList.length" :validate="validate" :detection="activeTab" @success="submit" />
          <AttachmentAudio v-else-if="['2'].includes(stepInfo.annexType)" ref="attachmentRef" v-model:file-list="fileList" :multiple="stepInfo.isMultipleAttachments === '1'" :validate="validate" @success="submit" />

          <view v-if="['2'].includes(stepInfo.preStepStatus)" class="absolute inset-0 size-full bg-[#282e3b] text-orange-500 flex flex-col space-y-1 items-center justify-center">
            <view class="i-carbon-time size-6"></view>
            <view class="text-xs">
              {{ stepInfo.tip || railwayStepPreStatusEnum[stepInfo.preStepStatus] }}
            </view>
          </view>
        </app-content>
      </view>
    </view>

    <WorkflowToolPreview ref="workflowToolPreviewRef" />

    <wd-message-box />
  </app-layout>
</template>

<style>
</style>
