<script setup>
import menuAssistant from '~@assets/images/index/menu-assistant.png?url'
import menuLibrary from '~@assets/images/index/menu-library.png?url'
import menuVehicle from '~@assets/images/index/menu-vehicle.png?url'
import menuWorkflow from '~@assets/images/index/menu-workflow.png?url'

const prompts = computed(() => [
  {
    label: '智慧作业',
    glassesOnly: true,
  },
  {
    label: '智慧检修',
    glassesOnly: true,
  },
])

const menuList = computed(() => [
  {
    label: '智慧作业',
    icon: menuWorkflow,
    action: () => {
      uni.navigateTo({
        url: '/pages/railway/overview/index',
      })
    },
  },
  {
    label: '智慧检修',
    icon: menuVehicle,
    action: () => {
      uni.navigateTo({
        url: '/pages/vehicle/scanner/index',
        // url: '/pages/vehicle/inspect/index',
        // url: '/pages/vehicle/scanner/live',
      })
    },
  },
  {
    label: '智慧料库',
    icon: menuLibrary,
    action: () => {
      uni.navigateTo({
        url: '/pages/warehouse/task/index',
      })
    },
  },
  {
    label: '智慧助手',
    icon: menuAssistant,
    action: () => {
      uni.navigateTo({
        url: '/pages/assistant/index',
      })
    },
  },
])
</script>

<template>
  <app-layout :prompts="prompts">
    <template #inset>
      <image
        src="~@assets/images/assistant/bg-gradient.png"
        mode="aspectFill"
        class="absolute inset-0 size-full"
      />
    </template>
    <view class="h-full flex items-center">
      <view v-for="(item, index) of menuList" :key="index" :aria-label="item.label" :title="item.label" class="w-25 flex flex-1 flex-col items-center justify-center rounded py-4 text-center space-y-2" @click="item.action">
        <image
          :src="item.icon"
          mode="aspectFit"
          class="size-14 flex-none"
        />
        <text class="button-text flex-none text-sm text-white">
          {{ item.label }}
        </text>
      </view>
    </view>
  </app-layout>
</template>
