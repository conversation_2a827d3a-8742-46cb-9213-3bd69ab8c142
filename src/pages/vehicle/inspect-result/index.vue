<script setup>
import { postVehicleItemCount } from '@/api/vehicle/index.js'

const statsInfo = ref({})

const queryParams = ref({})

onLoad((query) => {
  queryParams.value = query
})

async function getStatsInfo() {
  const params = {
    taskId: queryParams.value.taskId,
  }
  const res = await postVehicleItemCount(params).send(true)

  statsInfo.value = res.data || {}
}

onMounted(() => {
  getStatsInfo()
})
</script>

<template>
  <app-layout>
    <view class="size-full flex flex-col py-4 space-y-3">
      <view class="flex-none flex items-center justify-center text-[#28CFA7] space-x-1">
        <view class="i-carbon-checkmark-filled size-[34rpx] flex-none"></view>
        <view>恭喜您完成检修</view>
      </view>

      <view class="flex-1 h-0 rounded bg-[#282e3b] relative">
        <image
          v-if="abnormal"
          src="~@assets/images/railway/result/bg-error.png"
          mode="scaleToFill"
          class="size-full"
        />
        <image
          v-else
          src="~@assets/images/railway/result/bg-success.png"
          mode="scaleToFill"
          class="size-full"
        />
        <view class="absolute inset-0 size-full flex items-center">
          <view class="flex flex-col items-center flex-1 w-0 space-y-1">
            <view class="text-xl font-bold">
              {{ statsInfo.totalCompletedNum || 0 }}
            </view>

            <view class="text-2xs text-gray-400">
              总检修项
            </view>
          </view>

          <view class="w-px h-1/2 bg-gray-600 flex-none"></view>

          <view class="flex flex-col items-center flex-1 w-0 space-y-1">
            <view class="text-xl font-bold">
              {{ statsInfo.isCompletedNum || 0 }}
            </view>

            <view class="text-2xs text-gray-400">
              已完成
            </view>
          </view>

          <view class="w-px h-1/2 bg-gray-600 flex-none"></view>

          <view class="flex flex-col items-center flex-1 w-0 space-y-1">
            <view class="text-xl font-bold text-red-500">
              {{ statsInfo.exCompletedNum || 0 }}
            </view>

            <view class="text-2xs text-gray-400">
              异常项
            </view>
          </view>
        </view>
      </view>
    </view>
  </app-layout>
</template>

<style>
</style>
