<script setup>
import { postVehicleExceptionUpload } from '@/api/vehicle/index.js'
import { sleep } from '@/utils'

const content = ref('')
const fileList = ref([])
const queryParams = ref({})

const appImageUploadRef = ref()

const {
  isR<PERSON>ognizing,
  partialResult,
  finalResult,
  startR<PERSON><PERSON>nize,
  stopR<PERSON>ognize,
} = useSpeechRecognitionPlus({
  userInterface: true,
  simple: true,
})

const prompts = computed(() => {
  const value = []

  value.push({
    label: '语音',
    action: () => {
      startRecognize()
    },
  })

  if (fileList.value.length || content) {
    value.push({
      label: '上报',
      action: () => {
        onSubmitClick()
      },
    })
  }

  value.push({
    label: '取消',
    action: () => {
      uni.navigateBack()
    },
  })

  return value
})

watchEffect(() => {
  if (!partialResult.value && !finalResult.value) {
    return false
  }

  content.value = finalResult.value || partialResult.value
})

onLoad((query) => {
  queryParams.value = query
})

async function onSubmitClick() {
  if (!content.value && !fileList.value.length) {
    uni.showToast({
      title: '上报内容或照片不能为空',
      icon: 'none',
    })

    return false
  }

  const params = {
    taskType: '1',
    sceneType: '1',
    ...queryParams.value,
    annexUrl: fileList.value.join(','),
    remark: content.value,
  }

  const res = await postVehicleExceptionUpload(params)

  uni.showToast({
    title: res.msg || '上报成功',
    icon: 'none',
  })

  await sleep()

  uni.navigateBack()
}
</script>

<template>
  <app-layout :prompts="prompts">
    <app-content>
      <view class="flex flex-col size-full">
        <view class="flex-none flex items-center justify-center text-xs bg-[#323A4A] py-1 space-x-1">
          <image
            src="~@assets/images/vehicle/icon-warn.png"
            mode="aspectFit"
            class="size-[32rpx]"
          />

          <text class="">
            人工上报
          </text>
        </view>
        <view class="flex-1 h-0 flex space-x-1 p-2">
          <view class="flex-1 w-0 bg-black/40 rounded p-2">
            <textarea v-model="content" class="size-full text-2xs" placeholder="语音描述异常情况"></textarea>
          </view>
          <view class="flex-1 w-0 bg-black/40 rounded">
            <app-image-upload ref="appImageUploadRef" v-model:file-list="fileList" multiple />
          </view>
        </view>
      </view>
    </app-content>
  </app-layout>
</template>

<style>

</style>
