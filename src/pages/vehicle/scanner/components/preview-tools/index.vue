<script setup>
// import taskCar from '~@assets/images/vehicle/task-car.png'
// import taskFix from '~@assets/images/vehicle/task-fix.png'
// import taskTicket from '~@assets/images/vehicle/task-ticket.png'

const toolInfo = ref({})
const toolImage = ref('')

const visible = ref(false)

const fieldList = computed(() => {
  const value = [
    {
      // icon: taskTicket,
      label: '工作票号',
      value: toolInfo.value.workTicketNo,
    },
    {
      // icon: taskFix,
      label: '工区',
      value: toolInfo.value.workArea,
    },
    {
      // icon: taskCar,
      label: '作业车',
      value: toolInfo.value.workVehicle,
    },
    {
      // icon: taskFix,
      label: '正驾驶',
      value: toolInfo.value.workStDriver,
    },
    {
      // icon: taskFix,
      label: '副驾驶',
      value: toolInfo.value.workCoDriver,
    },
  ]

  return value
})

function open(params) {
  visible.value = true
  toolInfo.value = params?.data || {}
  toolImage.value = params?.image
}

function close() {
  visible.value = false
}

defineExpose({
  open,
  close,
  toolInfo,
})
</script>

<template>
  <view v-if="visible" class="absolute inset-0 size-full z-50 bg-[#121419]">
    <view class="flex size-full space-x-1 px-2 py-1">
      <view class="flex-1 w-0">
        <app-content>
          <view class="h-full flex flex-col">
            <view class="flex-none flex items-center bg-[#323A4A] px-2 py-1 space-x-1">
              <view class="app-marker"></view>
              <view class="text-xs">
                任务信息确定
              </view>
            </view>

            <scroll-view scroll-y class="h-0 flex-1">
              <view class="space-y-1 py-2 px-2">
                <view v-for="(item, index) of fieldList" :key="index" class="space-x-2 flex">
                  <view class="w-16 flex-none flex justify-end space-x-1">
                    <image
                      v-if="item.icon"
                      :src="item.icon"
                      mode="aspectFit"
                      class="flex-none size-4"
                    />

                    <view class="text-xs text-[#9BA6C1] flex-1 w-0 text-right">
                      {{ item.label }}:
                    </view>
                  </view>

                  <view class="flex-1 w-0 text-xs">
                    {{ item.value || '-' }}
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
        </app-content>
      </view>

      <view class="flex-none w-36">
        <app-content custom-class="flex items-center justify-center p-1">
          <image
            :src="toolImage"
            mode="aspectFit"
            class="h-full"
          />
        </app-content>
      </view>
    </view>
  </view>
</template>

<style>
</style>
