<script setup>
const visible = ref(false)
const imageUrl = ref('')

function open(src) {
  visible.value = true
  imageUrl.value = src
}
function close() {
  visible.value = false
  imageUrl.value = ''
}

defineExpose({
  open,
  close,
})
</script>

<template>
  <view v-if="visible" class="absolute inset-0 bg-[#121419] flex items-center justify-center">
    <image
      :src="imageUrl"
      mode="aspectFit"
      class="size-full"
    />

    <view class="absolute bottom-1 inset-x-0 flex items-center justify-center">
      <view class="text-xs rounded-full inline-block px-4 py-1 bg-black/70">
        识别中...
      </view>
    </view>
  </view>
</template>

<style>
</style>
