<script setup>
import { getVehicleItemGetCurrentInfo, postVehicleTaskAdd, postVehicleTaskIdentify } from '@/api/vehicle/index.js'
import { setStoreQuery, sleep } from '@/utils'
import { batchUpload } from '@/utils/upload/index.js'
import ImageLoading from './components/image-loading/index.vue'
import PreviewTools from './components/preview-tools/index.vue'

const queryParams = ref({})

const fileList = ref([])

const imageLoadingRef = ref()

const previewToolsRef = ref()

const currentAction = ref('')

const resultId = ref('')

const previewImage = ref('')

const prompts = computed(() => {
  const value = []

  if (currentAction.value === 'previewing') {
    value.push({
      label: '确定',
      action: () => {
        onSubmitClick()
      },
    })
    value.push({
      label: '取消',
      action: () => {
        previewImage.value = ''
        resultId.value = ''
        currentAction.value = ''
        fileList.value = []
        previewToolsRef.value.close()
        // onTakeClick()
      },
    })
  }

  return value
})

onLoad((query) => {
  queryParams.value = query
  getCurrentInspectInfo()
})

async function getCurrentInspectInfo() {
  const res = await getVehicleItemGetCurrentInfo().send(true)

  if (res.data?.id) {
    uni.showToast({
      title: '当前有任务未完成，正在跳转',
      icon: 'none',
    })

    const resultId = setStoreQuery(res.data)

    await sleep(2000)

    uni.redirectTo({
      url: `/pages/vehicle/inspect/index?resultId=${resultId}`,
    })
  }
}

async function onTakeClick() {
  try {
    const ret = await uni.chooseImage({
      count: 1,
      sizeType: ['original'],
      sourceType: ['camera'],
      // sizeType: ['original', 'compressed'],
    })

    previewImage.value = ret.tempFilePaths[0]

    const remoteFilePaths = await batchUpload(ret.tempFilePaths, { showProgress: true })

    fileList.value = [...remoteFilePaths]

    onScanClick()
  }
  catch (error) {
    console.warn(error.message || '拍照失败')
  }
}

async function onScanClick() {
  try {
    currentAction.value = 'scanning'
    imageLoadingRef.value.open(previewImage.value)

    const res = await postVehicleTaskIdentify({
      urls: fileList.value.join(','),
      taskId: queryParams.value.taskId,
    })

    await sleep(500)

    currentAction.value = 'previewing'

    previewToolsRef.value.open({
      data: res.data,
      image: previewImage.value,
    })
  }
  finally {
    imageLoadingRef.value.close()
  }
}

async function onSubmitClick() {
  const params = previewToolsRef.value.toolInfo

  await postVehicleTaskAdd(params)

  uni.redirectTo({
    url: '/pages/vehicle/inspect/index',
  })
}

function onBackClick() {
  if (['scanning'].includes(currentAction.value)) {
    return false
  }

  if (['previewing'].includes(currentAction.value)) {
    return false
  }

  uni.navigateBack()
}
</script>

<template>
  <app-layout :prompts="prompts" @back-click="onBackClick">
    <app-content border>
      <view class="flex flex-col items-center justify-center size-full" aria-label="拍照" @click="onTakeClick">
        <view class="absolute top-2 inset-center-x text-sm">
          扫描工作票
        </view>

        <image
          src="~@/assets/images/railway/steps/icon-camera.png"
          mode="aspectFit"
          class="size-6"
        />

        <view class="text-xs text-[#9BA6C1] mt-1">
          拍照
        </view>

        <view class="absolute bottom-2 inset-center-x text-2xs text-center whitespace-nowrap text-primary-500">
          请对准工作票，并保证拍照时工作票的完整性
        </view>
      </view>
    </app-content>
    <ImageLoading ref="imageLoadingRef" />
    <PreviewTools ref="previewToolsRef" />
  </app-layout>
</template>

<style lang="postcss" scoped>
</style>
