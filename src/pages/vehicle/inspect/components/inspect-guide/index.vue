<script>
export default {
  props: {
    inspectInfo: {
      type: Object,
      default: null,
    },
    inspectTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    close() {
      this.visible = false
    },
  },
}
</script>

<template>
  <view v-if="visible" class="inspect-guide">
    <!-- 左侧图片区域 -->
    <view class="inspect-guide__image-container">
      <image
        v-if="inspectInfo"
        :src="inspectInfo.itemImg"
        mode="aspectFit"
        class="inspect-guide__image"
      />
    </view>

    <!-- 右侧内容区域 -->
    <view class="inspect-guide__content">
      <!-- 头部标题区域 -->
      <view class="inspect-guide__header">
        <view class="inspect-guide__title">
          <text class="inspect-guide__title-text">
            检修内容
          </text>
        </view>

        <!-- <view class="inspect-guide__badge">
          <text class="inspect-guide__badge-text">
            重点
          </text>
        </view> -->
      </view>

      <!-- 任务点列表区域 -->
      <scroll-view class="inspect-guide__scroll" scroll-y="true">
        <view v-if="inspectInfo" class="inspect-guide__list">
          <view
            v-for="(item, index) of inspectInfo.taskPoints"
            :key="index"
            class="inspect-guide__list-item"
          >
            <!-- 序号圆圈 -->
            <view
              class="inspect-guide__list-number"
              :class="index === 0 ? 'inspect-guide__list-number--active' : 'inspect-guide__list-number--normal'"
            >
              <text class="inspect-guide__list-number-text">
                {{ index + 1 }}
              </text>
            </view>

            <!-- 任务点名称 -->
            <view class="inspect-guide__list-content">
              <text class="inspect-guide__list-text">
                {{ item.pointName }}
              </text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<style>
.inspect-guide {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  flex-direction: row;
  padding-top: 8rpx;
  padding-bottom: 8rpx;
  padding-left: 24rpx;
  padding-right: 24rpx;
}

.inspect-guide__image-container {
  width: 320rpx;
  background-color: #000000;
  align-items: center;
  justify-content: center;
  padding: 2rpx;
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
  overflow: hidden;
}

.inspect-guide__image {
  width: 316rpx;
}

.inspect-guide__content {
  flex: 1;
  background-color: #282e3b;
  padding: 16rpx;
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
  overflow: hidden;
  flex-direction: column;
}

.inspect-guide__header {
  flex-direction: row;
  padding-bottom: 8rpx;
}

.inspect-guide__badge {
  background-color: #3e3737;
  padding-left: 8rpx;
  padding-right: 8rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
  height: 30rpx;
  flex-direction: row;
  align-items: center;
}

.inspect-guide__badge-text {
  color: #ff8411;
  font-size: 24rpx;
}

.inspect-guide__title {
  flex: 1;
}

.inspect-guide__title-text {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
  line-height: 25rpx;
  lines: 2;
  text-overflow: ellipsis;
}

.inspect-guide__scroll {
  margin-top: 8rpx;
  flex: 1;
}

.inspect-guide__list {
  flex-direction: column;
}

.inspect-guide__list-item {
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.inspect-guide__list-number {
  width: 24rpx;
  height: 24rpx;
  border-radius: 28rpx;
  align-items: center;
  justify-content: center;
  margin-right: 8rpx;
}

.inspect-guide__list-number--active {
  background-color: #ff8411;
}

.inspect-guide__list-number--normal {
  background-color: #9ba6c1;
}

.inspect-guide__list-number-text {
  color: #1f2937;
  font-size: 16rpx;
  font-weight: bold;
  lines: 2;
  text-overflow: ellipsis;
}

.inspect-guide__list-content {
  flex: 1;
}

.inspect-guide__list-text {
  color: #9ba6c1;
  font-size: 20rpx;
  line-height: 24rpx;
}
</style>
