<script>
export default {
  props: {
    inspectInfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      visible: false,
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    close() {
      this.visible = false
    },
  },
}
</script>

<template>
  <view v-if="visible" class="inspect-diagram">
    <image
      :src="inspectInfo?.itemImg"
      mode="aspectFit"
      class="inspect-diagram__image"
    />
  </view>
</template>

<style>
.inspect-diagram {
  position: absolute;
  top: 8rpx;
  left: 24rpx;
  right: 24rpx;
  bottom: 8rpx;
  background-color: #000000;
  overflow: hidden;
  border-radius: 8rpx;
}

.inspect-diagram__image {
  @apply absolute inset-0;
}
</style>
