<script>
import iconWarn from '~@assets/images/vehicle/icon-warn.png'

export default {
  props: {
    inspectInfo: {
      type: Object,
      default: null,
    },
    inspectTitle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      iconWarn,
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    close() {
      this.visible = false
    },
  },
}
</script>

<template>
  <view v-if="visible" class="inspect-point">
    <view class="inspect-point__header">
      <text class="inspect-point__title">
        检修内容
      </text>
    </view>

    <!-- 检修点列表区域 -->
    <scroll-view class="inspect-point__scroll" scroll-y="true">
      <view v-if="inspectInfo" class="inspect-point__list">
        <view
          v-for="(item, index) of inspectInfo.taskPoints"
          :key="item.id"
          class="inspect-point__list-item"
        >
          <view
            class="inspect-guide__list-number"
            :class="true ? 'inspect-guide__list-number--active' : 'inspect-guide__list-number--normal'"
          >
            <text class="inspect-guide__list-number-text">
              {{ index + 1 }}
            </text>
          </view>

          <!-- 内容区域 -->
          <view class="inspect-point__content">
            <text
              class="inspect-point__content-text"
              :class="true ? 'inspect-point__content-text--important' : 'inspect-point__content-text--normal'"
            >
              {{ item.pointName }}
            </text>
          </view>

          <!-- 警告图标区域 -->
          <!-- <view class="inspect-point__icon-container">
            <image
              v-if="item.isKeyPoint === 1"
              :src="iconWarn"
              mode="aspectFit"
              class="inspect-point__icon"
            />
          </view> -->
        </view>
      </view>
      <view v-else class="inspect-point__empty">
        <text class="inspect-point__empty-text">
          暂无数据
        </text>
      </view>
    </scroll-view>
  </view>
</template>

<style>
.inspect-point {
  position: absolute;
  top: 8rpx;
  bottom: 8rpx;
  left: 24rpx;
  right: 24rpx;
  background-color: #282e3b;
  padding: 16rpx;
  border-radius: 16rpx;
  overflow: hidden;
  flex-direction: column;
}

.inspect-point__header {
  flex-direction: row;
  align-items: center;
  padding-bottom: 4rpx;
}

.inspect-point__title {
  font-size: 24rpx;
  color: #ffffff;
}

.inspect-point__scroll {
  flex: 1;
  height: 0;
}

.inspect-point__list {
  flex-direction: column;
}

.inspect-point__list-item {
  flex-direction: row;
  align-items: flex-start;
  margin-top: 24rpx;
}

.inspect-guide__list-number {
  width: 24rpx;
  height: 24rpx;
  border-radius: 999px;
  align-items: center;
  justify-content: center;
  margin-right: 8rpx;
}

.inspect-guide__list-number--active {
  background-color: #ff8411;
}

.inspect-guide__list-number--normal {
  background-color: #9ba6c1;
}

.inspect-guide__list-number-text {
  color: #1f2937;
  font-size: 16rpx;
  font-weight: bold;
  lines: 2;
  text-overflow: ellipsis;
  line-height: 24rpx;
}

.inspect-point__tag-container {
  margin-right: 4rpx;
}

.inspect-point__tag {
  padding-left: 16rpx;
  padding-right: 16rpx;
  padding-top: 4rpx;
  padding-bottom: 4rpx;
  border-radius: 8rpx;
}

.inspect-point__tag--important {
  background-color: rgba(255, 132, 17, 0.1);
}

.inspect-point__tag--normal {
  background-color: rgba(155, 166, 193, 0.1);
}

.inspect-point__tag-text {
  font-size: 20rpx;
}

.inspect-point__tag-text--important {
  color: #ff8411;
}

.inspect-point__tag-text--normal {
  color: #9ba6c1;
}

.inspect-point__content {
  flex: 1;
}

.inspect-point__content-text {
  font-size: 20rpx;
  line-height: 28rpx;
}

.inspect-point__content-text--important {
  color: #ffffff;
}

.inspect-point__content-text--normal {
  color: #9ba6c1;
}

.inspect-point__icon-container {
  align-self: flex-start;
  margin-left: 8rpx;
}

.inspect-point__icon {
  width: 24rpx;
  height: 24rpx;
  margin-top: 4rpx;
}

.inspect-point__empty {
  @apply absolute inset-0 flex items-center justify-center;
}

.inspect-point__empty-text {
  color: #9ba6c1;
  font-size: 20rpx;
}
</style>
