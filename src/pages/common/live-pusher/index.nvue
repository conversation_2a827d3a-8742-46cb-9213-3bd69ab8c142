<script>
import { sleep } from '@/utils'

export default {
  data() {
    return {
      liveFlag: false,
    }
  },
  computed: {
    prompts() {
      const value = []

      if (this.liveFlag) {
        value.push({
          label: '停止直播',
          action: () => { this.stopPusher() },
        })
      }
      else {
        value.push({
          label: '开始直播',
          action: () => { this.startPusher() },
        })
      }

      return value
    },
    pusherUrl() {
      return `${process.env.VITE_LIVE_PUSHER_URL}${this.pusherPath}`
    }
  },
  onLoad(query) {
    this.pusherPath = query.path
  },
  async onReady() {
    await this.$nextTick()
    this.$refs.pusherRef.init(this)
    this.$refs.pusherRef.startPreview()
    this.startPusher()
  },
  methods: {

    async startPusher() {
      this.$refs.pusherRef.start()
      this.liveFlag = true
    },
    async stopPusher() {
      this.$refs.pusherRef.stop()
      this.liveFlag = false
      await sleep()
      this.$refs.pusherRef?.startPreview?.()
    },
  },
}
</script>

<template>
  <app-layout-native class="" :prompts="prompts">
    <template v-if="liveFlag" #header-center>
      <view class="recording-tip">
        <image v-if="pushing" class="recording-tip__dot" src="~@assets/images/common/recording.gif" mode="aspectFit"></image>
        <text class="recording-tip__text">
          {{ pushing ? '直播中' : '连接中...' }}
        </text>
      </view>
    </template>

    <app-live-pusher ref="pusherRef" v-model:pushing="pushing" :path="pusherPath"></app-live-pusher>

    <!-- <view class="pusher-info">
      <text class="pusher-info__text">
        {{ pusherUrl }}
      </text>
    </view> -->
  </app-layout-native>
</template>

<style lang="postcss">
.app-live-pusher {
  flex: 1;
}

.recording-tip {
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.recording-tip__dot {
  width: 20rpx; 
  height: 20rpx;
}

.recording-tip__text {
  font-size: 24rpx;
  color: white;
}

.pusher-info {
 text-align: center;
 background-color: rgba(0,0,0,0.7);
 border-radius: 8rpx;
 padding: 4rpx 8rpx;
 font-size: 20rpx;
 position: absolute;
 bottom: 0;
 left: 0;
 right: 0;
}

.pusher-info__text {
 text-align: center;
 color: white;
}

</style>
