<script setup>
import { primaryColor } from '@/settings/index.mjs'

const props = defineProps({
  isStreaming: {
    type: Boolean,
    default: false,
  },
  isRecognizing: {
    type: Boolean,
    default: false,
  },
  volume: {
    type: Number,
    default: 0,
  },
  partialResult: {
    type: String,
    default: '',
  },
  finalResult: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['sendMessage'])

const conversationList = defineModel('conversationList', {
  type: Array,
  default: () => [],
})

const question = defineModel('question', {
  type: String,
  default: '',
})

const visible = defineModel('visible', {
  type: Boolean,
  default: false,
})

const waking = defineModel('waking', {
  type: Boolean,
  default: false,
})

const focusFlag = defineModel('focusFlag', {
  type: Boolean,
  default: false,
})

const scrollIntoView = ref('scroll-into-view')

const { scrollToBottom } = useScrollView({
  scrollIntoView,
})

// 计算属性
const speechPlaceholder = computed(() => {
  if (props.isRecognizing) {
    return '正在监听中，请说话...'
  }
  return '请输入你的问题或点击开始监听...'
})

// 监听对话内容变化，自动滚动
watch(() => {
  const lastItem = conversationList.value[conversationList.value.length - 1]
  const length = (lastItem?.content || '').length
  return length
}, () => {
  scrollToBottom()
}, {
  immediate: true,
})

// 监听语音识别结果，更新问题内容
watch(() => [props.partialResult, props.finalResult], ([partial, final]) => {
  if (waking.value && (partial || final)) {
    question.value = final || partial
  }
})

async function toggle() {
  visible.value = !visible.value

  await nextTick()

  if (visible.value) {
    scrollToBottom()
  }
}

async function onFocus() {
  scrollToBottom()
}

defineExpose({
  toggle,
})
</script>

<template>
  <view
    v-if="visible"
    class="absolute inset-0 size-full px-2 bg-black/30 backdrop-blur-md saturate-180 flex flex-col"
  >
    <scroll-view
      ref="scrollViewRef"
      scroll-y
      scroll-with-animation
      :scroll-into-view="scrollIntoView"
      class="h-full"
      :class="isStreaming || waking ? 'pb-8' : 'pb-2'"
    >
      <!-- 对话历史 -->
      <view
        v-for="(item, index) of conversationList"
        :key="index"
        class="pt-2"
      >
        <view
          class="text-xs break-words"
          :class="item.role === 'question' ? 'text-white/70' : 'text-white'"
        >
          <view
            v-if="item.role === 'question'"
            class="flex items-start space-x-2"
          >
            <view class="flex-none text-white/50">
              问:
            </view>
            <view class="flex-1 mt-[1rpx] w-0">
              {{ item.content || '-' }}
            </view>
          </view>
          <view
            v-else
            class="flex items-start space-x-2"
          >
            <view class="flex-none text-primary-400">
              答:
            </view>
            <view class="flex-1 mt-[1rpx]">
              <zero-markdown-view v-if="item.content" :markdown="item.content || '-'" :theme-color="primaryColor">
              </zero-markdown-view>
              <view
                v-if="!item.content && index === conversationList.length - 1 && isStreaming"
                class="inline-block w-1 h-3 bg-primary-400 animate-pulse ml-1"
              ></view>
            </view>
          </view>
        </view>
      </view>

      <view
        v-if="waking"
        class="flex items-start space-x-2 pt-2 text-xs"
      >
        <view class="flex-none text-primary-400">
          问:
        </view>

        <view class="flex-1 w-0 mt-[1rpx]">
          <textarea
            v-model="question"
            cursor-color="#3BFFF8"
            :placeholder="speechPlaceholder"
            :focus="focusFlag"
            auto-height
            class="flex-1 text-primary-500 text-xs w-full"
            placeholder-class="text-white/50"
            :disabled="isRecognizing"
            @focus="onFocus"
          ></textarea>
        </view>
      </view>

      <view id="scroll-into-view"></view>
    </scroll-view>
  </view>
</template>

<style lang="postcss">
</style>
