import EventStreamHandler from '@/utils/sse/index.js'

const defaultOptions = {
  baseUrl: process.env.VITE_API_COPILOT,
  headers: {
    // Authorization: 'Bearer ragflow-E3N2VjYzk2NWJmNTExZjA5YmNhMDI0Mm',
    Authorization: 'Bearer ragflow-E1ODg0MDJhNzRjZTExZjA4ZWU2MDI0Mm',
  },
}

export class VoiceAssistantStream {
  constructor(options = {}) {
    this.currentStream = null

    this.options = {
      ...defaultOptions,
      ...options,
    }

    this.baseApi = `${this.options.baseUrl}/api/v1/chats/${this.options.dialogId}`

    this.getSessionId()
  }

  async getSessionId() {
    const res = await uni.request({
      url: `${this.baseApi}/sessions`,
      method: 'POST',
      header: this.options.headers,
      data: {
        name: 'new session',
      },
    })

    this.sessionId = res?.data?.data?.id
  }

  // 开始对话流
  startConversation(options = {}) {
    const {
      sessionId,
      stream = true,
      onMessage,
      onError,
      onComplete,
    } = options

    const question = `${options.question} /no_think`

    // 停止当前流（如果存在）
    this.stopConversation()

    this.currentStream = new EventStreamHandler(`${this.baseApi}/completions`, {
      headers: this.options.headers,
      data: {
        session_id: sessionId || this.sessionId,
        question,
        stream,
      },
      onMessage: (data) => {
        if (data.answer) {
          data.answer = this.replaceAnswer(data.answer)
        }

        this.handleStreamMessage(data, onMessage, onError)
      },
      onError: (error) => {
        console.error('Stream error:', error)
        if (onError)
          onError(error)
      },
      onComplete: () => {
        console.log('Stream completed')
        if (onComplete)
          onComplete()
        this.currentStream = null
      },
    })

    this.currentStream.start()
    return this.currentStream
  }

  // 处理流式消息
  handleStreamMessage(data, onMessage, onError) {
    try {
      if (data.code === 0 && data.data) {
        const messageData = {
          answer: data.data.answer || '',
          reference: data.data.reference || {},
          audioBinary: data.data.audio_binary || null,
          sessionId: data.data.session_id || '',
          isComplete: false,
          rawData: data,
        }

        if (messageData.sessionId) {
          this.sessionId = messageData.sessionId
        }

        if (onMessage) {
          onMessage(messageData)
        }
      }
      else if (data.code !== 0) {
        // 处理错误响应
        throw new Error(`API Error: ${data.code}`)
      }
    }
    catch (error) {
      console.error('Message handling error:', error)
      onError(error)
    }
  }

  // 停止当前对话流
  stopConversation() {
    if (this.currentStream) {
      this.currentStream.stop()
      this.currentStream = null
    }
  }

  // 检查是否正在进行对话
  isActive() {
    return this.currentStream && this.currentStream.isRunning
  }

  static replaceAnswer(text) {
    const keywords = ['ID', 'id', '文档', 'Document']

    // 检查输入是否有效
    if (typeof text !== 'string') {
      console.error('错误：输入文本必须是字符串。')
      return text
    }

    if (!Array.isArray(keywords)) {
      console.error('错误：关键字必须是一个数组。')
      return text
    }

    // 正则表达式解释:
    // [\(（]  - 匹配一个英文左括号 `(` 或一个中文左括号 `（`
    // (.*?)   - 非贪婪模式捕获括号内的所有字符。这是捕获组 1。
    //   .     - 匹配除换行符外的任意字符
    //   *?    - 匹配0次或多次，且尽可能少地匹配（非贪婪）
    // [\)）]  - 匹配一个英文右括号 `)` 或一个中文右括号 `）`
    // g       - 全局搜索标志，确保替换所有匹配项，而不仅仅是第一个。
    const regex = /[(（](.*?)[)）]/g

    // 将所有关键字转换为小写，以便进行不区分大小写的比较
    const lowerCaseKeywords = keywords.map(k => k.toLowerCase())

    // 使用 String.prototype.replace() 和一个回调函数来进行条件替换
    return text.replace(regex, (match, innerContent) => {
    // match: 是正则表达式匹配到的完整字符串，例如 "(ID: 123)"
    // innerContent: 是第一个捕获组的内容，即括号内的文本，例如 "ID: 123"

      // 检查括号内的内容（转换为小写）是否包含任何一个关键字
      const hasKeyword = lowerCaseKeywords.some(keyword =>
        innerContent.toLowerCase().includes(keyword),
      )

      // 如果找到了关键字，则返回空字符串，即删除整个匹配项（括号和内容）。
      // 否则，返回原始匹配的字符串，即保留括号和内容不变。
      return hasKeyword ? '' : match
    })
  }
}
