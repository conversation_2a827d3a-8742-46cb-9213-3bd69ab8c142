<script setup>
const socketStore = useSocketStore()

socketStore.onError(() => {
  uni.showToast({
    icon: 'none',
    title: '连接服务器失败',
    duration: 1000,
  })
})

onLaunch(() => {
  console.log('App Launch')

  // #ifdef APP-PLUS
  plus.navigator.setFullscreen(true)
  plus.screen.lockOrientation('landscape-primary')
  // #endif
})

onShow(() => {
  console.log('App Show')
  
  socketStore.connect()
})

onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
// #ifndef APP-NVUE
@import '@unocss-applet/reset/uni-app/button-after.css';
@import '@unocss-applet/reset/uni-app/tailwind-compat.css';
// #endif
@import './styles/css/index.css';
</style>
