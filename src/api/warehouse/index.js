import alova from '@/api/index.js'

/** 获取料库任务列表 */
export function getMwhTaskList(data) {
  return alova.Get('/ar/mwh/task/list', { params: data, showError: true })
}

/** 图片 OCR 识别 */
export function postMwhPicIdentify(data) {
  return alova.Post('/ar/mwh/pic/identify', data, { showError: true })
}

/** 新增料库任务 */
export function postMwhTaskAdd(data) {
  return alova.Post('/ar/mwh/task/add', data, { showError: true })
}

/**  工具归还 */
export function postMwhTaskReceive(data) {
  return alova.Post('/ar/mwh/task/receive', data, { showError: true })
}

/**  工具领取 */
export function postMwhTaskReturn(data) {
  return alova.Post('/ar/mwh/task/return', data, { showError: true })
}

/** 根据任务 ID 获取任务详情 */
export function getMwhTask(taskId) {
  return alova.Get(`/ar/mwh/task/${taskId}`, { showError: true })
}

/** 根据任务 ID 获取任务下物料列表 */
export function getMwhMaterialList(data) {
  return alova.Get(`/ar/mwh/material/list`, { params: data, showError: true })
}

/** 出领取 根据识别结果获取更新库存后的比对结果 */
export function postMwhPicUpdate(data) {
  return alova.Post('/ar/mwh/pic/update', data, { showError: true })
}

/** 出领取 重置识别结果 */
export function postMwhTaskReset(data) {
  return alova.Post('/ar/mwh/task/reset', data, { showError: true })
}
