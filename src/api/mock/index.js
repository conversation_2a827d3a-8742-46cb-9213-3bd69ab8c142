import { createAlovaMockAdapter, defineMock } from '@alova/mock'
import adapterFetch from 'alova/fetch'
import { appExtra } from '@/settings/index.mjs'

function mockRequestAdapter() {
  const userMock = defineMock({
    '[POST]/login': {
      token: 'mock-token',
      code: 200,
    },
    '/getInfo': {
      data: {
        ...appExtra,
        userId: 1,
        username: appExtra.name,
      },
      code: 200,
    },
  })

  const railwayMock = defineMock({
    '[GET]/ar/getWorkflowListInfo': {
      data: {
        workflowList: [
          {
            id: '1',
            workflowName: '通知全体作业组成员返回',
            status: 'completed',
            assignee: '李安全员',
            role: '地线监护人',
            createTime: '2023-10-23 14:53:03',
            updateTime: '2023-10-23 15:23:03',
          },
          {
            id: '2',
            workflowName: '召开收工会',
            status: 'pending',
            assignee: '王工程师',
            role: '作业负责人',
            createTime: '2023-10-23 15:23:03',
            updateTime: '2023-10-23 15:23:03',
          },
          {
            id: '3',
            workflowName: '检查设备状态',
            status: 'in_progress',
            assignee: '张技术员',
            role: '技术员',
            createTime: '2023-10-23 15:30:03',
            updateTime: '2023-10-23 15:45:03',
          },
        ],
        userList: [
          {
            id: '1',
            name: '李安全员',
            role: '地线监护人',
            status: 'online',
            avatar: '',
            workflowId: '1',
          },
          {
            id: '2',
            name: '王工程师',
            role: '作业负责人',
            status: 'online',
            avatar: '',
            workflowId: '2',
          },
          {
            id: '3',
            name: '张技术员',
            role: '技术员',
            status: 'online',
            avatar: '',
            workflowId: '3',
          },
          {
            id: '4',
            name: '刘监护员',
            role: '监护员',
            status: 'offline',
            avatar: '',
            workflowId: '1',
          },
          {
            id: '5',
            name: '陈操作员',
            role: '操作员',
            status: 'online',
            avatar: '',
            workflowId: '2',
          },
        ],
      },
      code: 200,
    },
    '[GET]/ar/getWorkflowDetailInfo': {
      data: {
        id: '1',
        workflowName: '通知全体作业组成员返回',
        description: '作业完成后，通知所有作业组成员安全返回指定位置',
        status: 'completed',
        assignee: '李安全员',
        role: '地线监护人',
        createTime: '2023-10-23 14:53:03',
        updateTime: '2023-10-23 15:23:03',
        steps: [
          {
            id: '1',
            stepName: '通知全体作业组成员返回',
            status: 'completed',
            completedTime: '2023-10-23 15:20:03',
          },
          {
            id: '2',
            stepName: '召开收工会',
            status: 'pending',
            completedTime: null,
          },
        ],
        participants: [
          {
            id: '1',
            name: '李安全员',
            role: '地线监护人',
            status: 'online',
          },
          {
            id: '2',
            name: '王工程师',
            role: '作业负责人',
            status: 'online',
          },
        ],
        videoUrl: 'rtmp://*************:8650/AR-2025060701/202506170037?user=admin&pass=admin123',
      },
      code: 200,
    },
  })

  return createAlovaMockAdapter([userMock, railwayMock], {
    httpAdapter: adapterFetch(),
    matchMode: 'methodurl',
    enable: true,
    delay: 1000,
    mockRequestLogger: true,
    onMockResponse: async (response) => {
      return {
        response: {
          ...response,
          data: response.body,
        },
      }
    },
  })
}

export default mockRequestAdapter
