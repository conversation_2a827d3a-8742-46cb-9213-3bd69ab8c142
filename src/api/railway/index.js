import alova from '@/api/index.js'

/** 获取作业人员环节信息 */
export function getWorkflowListByUserNo(data) {
  return alova.Get('/ar/getWorkflowListByUserNo', { params: data, showError: true })
}

/** 开始当前环节 */
export function postStartWorkflowStep(data) {
  return alova.Post('/ar/startWorkflowStep', data)
}

/** 提交当前环节附件 */
export function postSubmitWorkflowStepAttach(data) {
  return alova.Post('/ar/submitWorkflowStepAttach', data)
}

/** 根据环节 ID 获取人脸识别人员列表 */
export function getPersonnelInformation(data) {
  return alova.Get('/ar/getPersonnelInformation', { params: data })
}

/** 根据图片获取模型检测结果 */
export function postCallModelByScene(data) {
  return alova.Post('/ar/callModelByScene', data, { showError: true })
}

/** 获取安全管控任务完成后的统计数据 */
export function getTaskStatistics(data) {
  return alova.Get('/ar/getTaskStatistics', { params: data })
}

/** 获取安全管控任务 完成事项列表 */
export function getWorkflowListInfoByTaskId(data) {
  return alova.Get('/ar/getWorkflowListInfoByTaskId', { params: data })
}

/** 获取工作流列表信息 */
export function getWorkflowListInfo(data) {
  return alova.Get('/ar/getWorkflowListInfo', { params: data, showError: true })
}

/** 获取工作流详细信息 */
export function getWorkflowDetailInfo(data) {
  return alova.Get('/ar/getWorkflowDetailInfo', { params: data, showError: true })
}

/** 工器具出入库识别结果确定保存 */
export function postSaveUtensilInfo(data) {
  return alova.Post('/ar/saveUtensilInfo', data, { showError: true })
}

/** 工器具出入库识别结果列表查询 */
export function getUtensilInfoListByWorkflowId(data) {
  return alova.Get('/ar/getUtensilInfoListByWorkflowId', { params: data, showError: true })
}

/** 异常事件-上报 */
export function postReportEvent(data) {
  return alova.Post('/ar/reportEvent', data, { showError: true })
}

/** 异常事件-根据环节 ID 获取异常事件信息 */
export function getEventInfo(data) {
  return alova.Get('/ar/getEventInfo', { params: data })
}
