import alova from '@/api/index.js'

/** OCR 识别获取车辆检修任务信息 */
export function postVehicleTaskIdentify(params = {}) {
  return alova.Post('/ar/vehicle/task/identify', params)
}

/** 新增车辆检修任务 */
export function postVehicleTaskAdd(params = {}) {
  return alova.Post('/ar/vehicle/task/add', params)
}

/** 获取当前检修项详情 */
export function getVehicleItemGetCurrentInfo(params = {}) {
  return alova.Get('/ar/vehicle/item/getCurrentInfo', params)
}

/** 人工上报 */
export function postVehicleExceptionUpload(params = {}) {
  return alova.Post('/ar/vehicle/exception/upload', params)
}

/**  检修项统计 */
export function postVehicleItemCount(params = {}) {
  return alova.Post('/ar/vehicle/item/count', params)
}

/**  强制结束 */
export function postVehicleItemUpdate(params = {}) {
  return alova.Post('/ar/vehicle/item/update', params)
}

/**  强制结束 */
export function postVehicleTaskDone(params = {}) {
  return alova.Post('/ar/vehicle/task/done', params)
}

/** 引导开关 */
export function postVehicleTaskEnabled(params = {}) {
  return alova.Post('/ar/vehicle/task/enabled', params, {
    hiddenError: true,
  })
}
