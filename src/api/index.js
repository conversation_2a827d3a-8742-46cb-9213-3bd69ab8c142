import uniappAdapter from '@alova/adapter-uniapp'
import { isH5 } from '@uni-helper/uni-env'
import { createAlova } from 'alova'
import mockRequestAdapter from './mock'

function useAdapter(isMock) {
  if (isMock) {
    return {
      requestAdapter: mockRequestAdapter(),
    }
  }

  return uniappAdapter()
}

function getBaseURL() {
  let value = process.env.VITE_API_ORIGIN + process.env.VITE_API_PATH

  if (process.env.VITE_PROXY_USE === '1' && isH5) {
    value = process.env.VITE_PROXY_PATH
  }

  return value
}

const alova = createAlova({
  ...useAdapter(process.env.VITE_API_MOCK === '1'),

  baseURL: getBaseURL(),

  beforeRequest: (method) => {
    console.log(`request:${method.url}`, method.data || method.config.params)

    const userStore = JSON.parse(uni.getStorageSync('user') || '{}')

    if (userStore.token) {
      // Bearer
      Object.assign(method.config.headers, {
        [process.env.VITE_API_TOKEN_KEY]: `${userStore.token}`,
        clientId: `${userStore.loginInfo.clientId}`,
      })
    }
  },

  responded: {
    onSuccess: async (response, options) => {
      console.log(`response:${options.url}`, response.data)

      if (typeof response.data === 'string') {
        try {
          response.data = JSON.parse(response.data)
        }
        catch (error) {
          console.warn(error.message || '捕获请求数据解析错误')
        }
      }
      if (response.status >= 400) {
        throw new Error(response.statusText)
      }

      const data = response.data

      if (data.code !== Number(process.env.VITE_API_SUCCESS_CODE)) {
        const hiddenError = options.config.hiddenError

        if (!hiddenError) {
          uni.showToast({
            title: data.msg,
            icon: 'none',
            mask: true,
          })
        }

        if ([401, 403].includes(data.code)) {
          uni.reLaunch({ url: '/pages/login/index' })
        }

        throw data
      }

      return data
    },

    onError: (error) => {
      console.error(`alova.responded.onError:${error.message || error}`)
    },

    onComplete: () => {},
  },
})

export default alova
