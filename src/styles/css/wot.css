:root,
page {
  --wot-color-theme: rgba(var(--primary-color-500), 1);
}

.wd-table.wd-table--designer {
  @apply overflow-hidden rounded;

  --wot-dark-background: transparent;
  --wot-table-bg: transparent;

  --wot-table-bg: #282e3b;
  --wot-dark-background2: #282e3b;

  --wot-table-stripe-bg: #1f242e;
  --wot-dark-background4: #1f242e;

  --wot-table-color: #ffffff;
  --wot-table-font-size: 20rpx;

  .wd-table__content--header {
    --wot-table-stripe-bg: #323a4a;
    --wot-dark-background4: #323a4a;
    --wot-table-color: #9ba6c1;
    --wot-dark-color: #9ba6c1;
  }

  .wd-table__header,
  .wd-table__cell {
    @apply min-h-[40rpx] !h-[40rpx];
  }
}

.wd-table--designer--plain {
  @apply overflow-hidden rounded;

  --wot-dark-background: transparent;
  --wot-table-bg: transparent;

  --wot-table-bg: transparent;
  --wot-dark-background2: transparent;

  --wot-table-stripe-bg: transparent;
  --wot-dark-background4: transparent;

  --wot-table-color: #ffffff;
  --wot-dark-color: #ffffff;

  --wot-table-font-size: 16rpx;

  --wot-dark-border-color: #333a4a;

  .wd-table__content--header {
    --wot-table-stripe-bg: transparent;
    --wot-dark-background4: rgba(255, 255, 255, 0.1);
  }
}

.wd-upload--designer {
  --wot-upload-size: 3rem;
  .wd-upload__evoke {
    --wot-upload-evoke-bg: rgba(var(--color-primary-500), 1);
    --wot-upload-evoke-color: rgba(var(--color-primary-50), 1);
    @apply !leading-none;
  }
}
