import { Enum } from 'enum-plus'

export const roleTypeEnum = Enum({
  leader: '工作领导人',
  contact: '驻站联络人',
  guardian: '地线监护人',
})

export const warehouseTaskStatusEnum = Enum({
  1: '待领取',
  2: '待归还',
  3: '已结束',
})

export const warehouseDiffStatusEnum = Enum({
  0: '不一致',
  1: '一致',
})

export const railwayDetectionEnum = Enum({
  face: '人员清点',
  helmet_workwear: '着装检查',
  insulating_gloves_boots: '穿戴检查',
  grounding_wire: '接地靴',
  upper_utensil: '工具清点',
  lower_utensil: '工具清点',
  voltage_detection: '验电检测',
  hang_grounding_pole: '地线检测',
})

export const railwayStepPreStatusEnum = Enum({
  1: '已完成',
  2: '等待中',
})
