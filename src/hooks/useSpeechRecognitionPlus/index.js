export function useSpeechRecognitionPlus(options) {
  // 响应式状态
  const isRecognizing = ref(false)
  const isSupported = ref(false)
  const partialResult = ref('')
  const finalResult = ref('')
  const volume = ref(0)
  const error = ref(null)

  // 配置选项
  const defaultOptions = {
    engine: 'baidu',
    lang: 'zh-cn',
    continue: false,
    userInterface: false,
    punctuation: true,
    timeout: 10000,
    nbest: 1,
    ...options,
  }

  if (options?.simple) {
    onMounted(() => {
      initialize()
    })

    onBeforeUnmount(() => {
      cleanup()
    })
  }

  // 检查设备支持
  function checkSupport() {
    try {
      // 检查是否在 UniApp 环境中
      if (typeof plus === 'undefined') {
        console.warn('语音识别: 不在 UniApp 环境中')
        return false
      }

      // 检查是否支持语音识别
      if (!plus.speech) {
        console.warn('语音识别: 设备不支持语音识别功能')
        return false
      }

      return true
    }
    catch (err) {
      console.error('语音识别: 检查支持性时出错', err)
      return false
    }
  }

  // 初始化
  function initialize() {
    // 等待 plus 准备就绪
    if (typeof plus !== 'undefined') {
      isSupported.value = checkSupport()
      setupEventListeners()
    }
    else {
      // 监听 plusready 事件
      document.addEventListener('plusready', () => {
        isSupported.value = checkSupport()
        if (isSupported.value) {
          setupEventListeners()
        }
      }, false)
    }
  }

  // 设置事件监听器
  function setupEventListeners() {
    if (!isSupported.value)
      return

    try {
      // 开始识别事件
      plus.speech.addEventListener('start', () => {
        console.log('语音识别: 开始')
        isRecognizing.value = true
        error.value = null
      }, false)

      // 音量变化事件
      plus.speech.addEventListener('volumeChange', (e) => {
        volume.value = e.volume || 0
      }, false)

      // 临时识别结果
      plus.speech.addEventListener('recognizing', (e) => {
        partialResult.value = e.partialResult || ''
      }, false)

      // 最终识别结果
      plus.speech.addEventListener('recognition', (e) => {
        finalResult.value = e.result || ''
        if (e.results && e.results.length > 0) {
          console.log('语音识别: 所有候选结果', e.results)
        }
      }, false)

      // 结束识别事件
      plus.speech.addEventListener('end', () => {
        console.log('语音识别: 结束')
        isRecognizing.value = false
        volume.value = 0
      }, false)

      // 错误事件
      plus.speech.addEventListener('error', (e) => {
        console.error('语音识别: 错误', e)

        error.value = {
          code: e.code || -1,
          message: `没有识别到语音已自动停止(${e.code || -1})`,
        }

        isRecognizing.value = false
        volume.value = 0
      }, false)
    }
    catch (err) {
      console.error('语音识别: 设置事件监听器失败', err)
      error.value = {
        code: -1,
        message: `设置事件监听器失败: ${err.message}`,
      }
    }
  }

  // 开始语音识别
  function startRecognize(innerOptions = {}) {
    if (!isSupported.value) {
      error.value = {
        code: -1,
        message: '设备不支持语音识别功能',
      }
      return Promise.reject(error.value)
    }

    if (isRecognizing.value) {
      console.warn('语音识别: 正在识别中，请先停止当前识别')
      return Promise.reject({ message: '正在识别中' })
    }

    return new Promise((resolve, reject) => {
      try {
        // 清空之前的结果
        partialResult.value = ''
        finalResult.value = ''
        error.value = null
        volume.value = 0

        // 合并配置选项
        const recognizeOptions = { ...defaultOptions, ...innerOptions }

        console.log('语音识别: 开始识别，配置:', recognizeOptions)

        // 启动语音识别
        plus.speech.startRecognize(
          recognizeOptions,
          (result) => {
            console.log('语音识别: 成功回调', result)
            finalResult.value = result
            resolve(result)
          },
          (err) => {
            console.error('语音识别: 失败回调', err)
            const errorInfo = {
              code: err.code || -1,
              message: err.message || '语音识别失败',
            }
            error.value = errorInfo
            reject(errorInfo)
          },
        )
      }
      catch (err) {
        console.error('语音识别: 启动失败', err)
        const errorInfo = {
          code: -1,
          message: `启动语音识别失败: ${err.message}`,
        }
        error.value = errorInfo
        reject(errorInfo)
      }
    })
  }

  // 停止语音识别
  function stopRecognize() {
    if (!isSupported.value) {
      console.warn('语音识别: 设备不支持语音识别功能')
      return
    }

    if (!isRecognizing.value) {
      console.warn('语音识别: 当前没有进行语音识别')
      return
    }

    try {
      console.log('语音识别: 停止识别')
      plus.speech.stopRecognize()
    }
    catch (err) {
      console.error('语音识别: 停止失败', err)
      error.value = {
        code: -1,
        message: `停止语音识别失败: ${err.message}`,
      }
    }
  }

  // 清空结果
  function clearResults() {
    partialResult.value = ''
    finalResult.value = ''
    error.value = null
    volume.value = 0
  }

  // 清理资源
  function cleanup() {
    if (isRecognizing.value) {
      stopRecognize()
    }
    clearResults()
  }

  return {
    // 状态
    isRecognizing,
    isSupported,
    partialResult,
    finalResult,
    volume,
    error,

    // 方法
    initialize,
    startRecognize,
    stopRecognize,
    clearResults,
    cleanup,
  }
}

export default useSpeechRecognitionPlus
