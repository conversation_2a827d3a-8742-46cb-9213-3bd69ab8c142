// import * as speechRecognition from '@/uni_modules/YL-SpeechUts'

// console.log('speechRecognition', speechRecognition)

// export function useSpeechRecognitionPlus() {
//   speechRecognition.init('ee24dd0f')
//   speechRecognition.createTts()
//   speechRecognition.createIat()

//   const voiceText = ref('')

//   function textToVoice(text) {
//     console.log('speechRecognition', speechRecognition)

//     speechRecognition.textToVoice(text, (res) => {
//       console.log('textToVoice.res', res)
//     })

//     return {
//       speechRecognition,
//       stop: () => {
//         speechRecognition?.stopSpeaking?.()
//       },
//     }
//   }

//   function voiceToText() {
//     speechRecognition.voiceToText((res) => {
//       console.log('voiceToText.res', res)
//     })
//   }

//   onBeforeUnmount(() => {
//     speechRecognition?.destroy?.()
//   })

//   return {
//     speechRecognition,
//     textToVoice,
//     voiceToText,
//     voiceText,
//   }
// }

// export default useSpeechRecognitionPlus
