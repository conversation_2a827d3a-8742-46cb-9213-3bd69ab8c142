import CryptoJS from 'crypto-js'

// 配置管理模块
export function createConfigManager(defaultOptions) {
  const config = { ...defaultOptions }

  function getConfig() {
    return { ...config }
  }

  function setConfig(newConfig) {
    Object.assign(config, newConfig)
  }

  return {
    getConfig,
    setConfig,
  }
}

// 时间戳生成模块
export function createDateStringGenerator() {
  function getDateString() {
    return new Date().toUTCString()
  }

  return {
    getDateString,
  }
}

// 鉴权 URL 生成模块
export function createAuthUrlGenerator(dateStringGenerator) {
  function generateAuthUrl(config) {
    const { apiKey, apiSecret } = config
    const host = 'tts-api.xfyun.cn'
    const path = '/v2/tts'
    const date = dateStringGenerator.getDateString()

    // 构建签名字符串
    const signatureOrigin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`

    // 使用 HMAC-SHA256 签名
    const signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret)
    const signature = CryptoJS.enc.Base64.stringify(signatureSha)

    // 构建 authorization 字符串
    const authorizationOrigin = `api_key="${apiKey}", algorithm="hmac-sha256", headers="host date request-line", signature="${signature}"`
    const authorization = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(authorizationOrigin))

    return `wss://${host}${path}?host=${host}&authorization=${authorization}&date=${date}`
  }

  return {
    generateAuthUrl,
  }
}

// WebSocket 管理模块
export function createWebSocketManager(authUrlGenerator, messageHandler) {
  let socketTask = null

  function createWebSocket(config) {
    return new Promise((resolve, reject) => {
      try {
        const url = authUrlGenerator.generateAuthUrl(config)

        socketTask = uni.connectSocket({
          url,
          success: () => {},
          fail: (err) => {
            console.error('WebSocket Fail:', err)
            messageHandler.handleError('连接失败')
            reject(err)
          },
        })

        socketTask.onOpen(() => {
          console.log('WebSocket 连接成功')
          messageHandler.handleConnectionOpen()
          resolve()
        })

        socketTask.onMessage((event) => {
          messageHandler.handleMessage(event.data)
        })

        socketTask.onError((error) => {
          console.error('WebSocket 错误:', error)
          messageHandler.handleError('连接失败')
          reject(error)
        })

        socketTask.onClose((event) => {
          console.log('WebSocket 连接关闭:', event.code, event.reason)
          messageHandler.handleConnectionClose()
        })
      }
      catch (error) {
        console.error('创建 WebSocket 失败:', error)
        reject(error)
      }
    })
  }

  function sendMessage(data) {
    if (socketTask) {
      // console.log('sendMessage.data', data.data.status)
      socketTask.send({
        data: JSON.stringify(data),
      })
    }
  }

  function closeWebSocket() {
    if (socketTask) {
      socketTask.close()
      socketTask = null
    }
  }

  return {
    createWebSocket,
    sendMessage,
    closeWebSocket,
  }
}

// 消息处理模块
export function createMessageHandler(state, audioManager) {
  let errorCallback = null

  function handleMessage(data) {
    try {
      const response = JSON.parse(data)

      // console.log('handleMessage.code', response.code)

      if (response.code !== 0) {
        state.error = `合成失败: ${response.message} (${response.code})`
        state.isSynthesizing = false
        // 通知主模块合成失败
        errorCallback?.(new Error(state.error))
        return
      }

      if (response.data && response.data.audio) {
        // 收集音频数据
        audioManager.collectAudioChunk(response.data.audio)

        // 更新进度
        if (response.data.ced) {
          state.progress = Number.parseInt(response.data.ced)
        }

        // 检查是否合成完成
        if (response.data.status === 2) {
          state.isSynthesizing = false
          audioManager.saveAndPlayAudio()
        }
      }
    }
    catch (error) {
      console.error('解析消息失败:', error)
      state.error = '数据解析失败'
      // 通知主模块解析失败
      errorCallback?.(error)
    }
  }

  function handleError(errorMessage) {
    state.error = errorMessage
    state.isConnecting = false
    // 通知主模块连接错误
    errorCallback?.(new Error(errorMessage))
  }

  function handleConnectionOpen() {
    state.isConnected = true
    state.isConnecting = false
  }

  function handleConnectionClose() {
    state.isConnected = false
    state.isConnecting = false
  }

  function setErrorCallback(callback) {
    errorCallback = callback
  }

  return {
    handleMessage,
    handleError,
    handleConnectionOpen,
    handleConnectionClose,
    setErrorCallback,
  }
}

// 音频管理模块
export function createAudioManager(state) {
  let audioChunks = []
  let audioContext = null
  let completedCallback
  let playStartedCallback
  let timeoutResetCallback // 新增：超时重置回调

  function collectAudioChunk(chunk) {
    audioChunks.push(chunk)
  }

  function resetAudioChunks() {
    audioChunks = []
  }

  function mergeAudioChunk(chunks) {
    const mergedBinary = chunks.map(atob).join('')
    return btoa(mergedBinary)
  }

  async function saveAndPlayAudio() {
    try {
      // 合并所有音频片段
      const audioBase64 = mergeAudioChunk(audioChunks)
      const fileName = `tts_${Date.now()}.mp3`

      // 兼容多端的音频保存和播放
      if (typeof plus !== 'undefined' && plus.io) {
        // App端使用 plus.io
        saveAndPlayWithPlusIO(audioBase64, fileName)
      }
      else {
        // 其他端使用 uni.getFileSystemManager 或直接播放base64
        saveAndPlayWithUniAPI(audioBase64, fileName)
      }
    }
    catch (error) {
      console.error('保存音频失败:', error)
      state.error = '音频处理失败'
      state.isPlaying = false
      completedCallback?.(error)
    }
  }

  // App端使用 plus.io 保存文件
  function saveAndPlayWithPlusIO(audioBase64, fileName) {
    plus.io.requestFileSystem(
      plus.io.PRIVATE_DOC,
      (fs) => {
        fs.root.getFile(
          fileName,
          { create: true },
          (fileEntry) => {
            fileEntry.createWriter((writer) => {
              writer.onwrite = () => {
                console.log('音频文件保存成功:', fileEntry.fullPath)
                state.audioUrl = fileEntry.fullPath
                playAudio(fileEntry.fullPath, () => {
                  fileEntry.remove()
                })
              }

              writer.onerror = (error) => {
                console.error('保存文件失败:', error)
                state.error = '保存音频失败'
                state.isPlaying = false
                completedCallback?.(error)
              }

              writer.writeAsBinary(audioBase64)
            })
          },
          (error) => {
            console.error('创建文件失败:', error)
            state.error = '创建音频文件失败'
            state.isPlaying = false
            completedCallback?.(error)
          },
        )
      },
      (error) => {
        console.error('请求文件系统失败:', error)
        state.error = '访问文件系统失败'
        state.isPlaying = false
        completedCallback?.(error)
      },
    )
  }

  // 其他端使用 uni API 或直接播放base64
  function saveAndPlayWithUniAPI(audioBase64, fileName) {
    try {
      // 尝试使用 uni.getFileSystemManager (小程序端)
      if (uni.getFileSystemManager) {
        const fs = uni.getFileSystemManager()
        const filePath = `${uni.env.USER_DATA_PATH}/${fileName}`

        // 将base64转换为ArrayBuffer
        const buffer = uni.base64ToArrayBuffer(audioBase64)

        fs.writeFile({
          filePath,
          data: buffer,
          success: () => {
            console.log('音频文件保存成功:', filePath)
            state.audioUrl = filePath
            playAudio(filePath, () => {
              // 清理临时文件
              fs.unlink({
                filePath,
                success: () => console.log('临时文件已清理'),
                fail: err => console.warn('清理临时文件失败:', err),
              })
            })
          },
          fail: (error) => {
            console.error('保存文件失败:', error)
            // 降级到直接播放base64
            playAudioFromBase64(audioBase64)
          },
        })
      }
      else {
        // 直接播放base64 (H5端等)
        playAudioFromBase64(audioBase64)
      }
    }
    catch (error) {
      console.error('使用uni API保存失败:', error)
      // 降级到直接播放base64
      playAudioFromBase64(audioBase64)
    }
  }

  // 直接播放base64音频数据
  function playAudioFromBase64(audioBase64) {
    try {
      const dataUrl = `data:audio/mp3;base64,${audioBase64}`
      console.log('直接播放base64音频')
      state.audioUrl = dataUrl
      playAudio(dataUrl, () => {
        // base64数据无需清理
      })
    }
    catch (error) {
      console.error('播放base64音频失败:', error)
      state.error = '音频播放失败'
      state.isPlaying = false
      completedCallback?.(error)
    }
  }

  function playAudio(filePath, callback) {
    try {
      if (audioContext) {
        audioContext.destroy()
      }

      // 使用 uni.createInnerAudioContext 播放音频，兼容多端
      audioContext = uni.createInnerAudioContext()
      audioContext.src = filePath

      // 记录播放开始时间
      const playStartTime = Date.now()
      const MIN_PLAY_DURATION = 2000 // 最少播放2秒
      let lastProgressTime = Date.now() // 记录最后一次进度更新时间

      // 创建一个包装的回调函数，确保至少2秒后才执行
      const wrappedCallback = () => {
        const playDuration = Date.now() - playStartTime
        const remainingTime = Math.max(0, MIN_PLAY_DURATION - playDuration)

        if (remainingTime > 0) {
          console.log(`播放时长不足2秒，等待 ${remainingTime}ms 后执行回调`)
          setTimeout(() => {
            callback?.()
            completedCallback?.()
          }, remainingTime)
        }
        else {
          callback?.()
          completedCallback?.()
        }
      }

      // 监听音频播放完成事件
      audioContext.onEnded(() => {
        state.isPlaying = false
        console.log('音频播放完成')
        wrappedCallback()
      })

      // 监听音频播放错误事件
      audioContext.onError((error) => {
        state.isPlaying = false
        state.error = '音频播放失败'
        console.error('音频播放错误:', error)
        wrappedCallback()
      })

      // 监听音频播放进度事件，用于智能重置超时定时器
      let lastResetTime = 0 // 记录上次重置超时定时器的时间
      const RESET_INTERVAL = 10000 // 重置间隔：10秒，避免频繁重置

      audioContext.onTimeUpdate(() => {
        const currentTime = Date.now()
        const timeSinceLastProgress = currentTime - lastProgressTime
        const timeSinceLastReset = currentTime - lastResetTime

        // 如果距离上次进度更新超过5秒，说明音频可能卡住了，不重置超时
        if (timeSinceLastProgress < 5000) {
          // 只有距离上次重置超过指定间隔时才重置，避免频繁重置
          if (timeSinceLastReset >= RESET_INTERVAL) {
            timeoutResetCallback?.()
            lastResetTime = currentTime
            console.log('音频播放进度正常，重置超时定时器')
          }
        }
        else {
          console.warn('音频播放进度异常，不重置超时定时器')
        }

        lastProgressTime = currentTime
      })

      // 监听音频可以播放事件
      audioContext.onCanplay(() => {
        console.log('音频可以播放')
        audioContext.play()
        state.isPlaying = true

        // 通知播放开始，用于启动超时定时器
        playStartedCallback?.()
      })

      // 开始加载音频
      console.log('开始加载音频:', filePath)
    }
    catch (error) {
      console.error('播放音频失败:', error)
      state.error = '音频播放失败'
      state.isPlaying = false
      completedCallback?.(error)
    }
  }

  function stopPlay() {
    if (audioContext) {
      audioContext.stop()
      state.isPlaying = false
    }
  }

  function pausePlay() {
    if (audioContext) {
      audioContext.pause()
      state.isPlaying = false
    }
  }

  function resumePlay() {
    if (audioContext) {
      audioContext.play()
      state.isPlaying = true
    }
  }

  function closeAudio() {
    if (audioContext) {
      audioContext.destroy()
      audioContext = null
    }
  }

  function onCompleted(callback) {
    completedCallback = callback
  }

  function onPlayStarted(callback) {
    playStartedCallback = callback
  }

  function onTimeoutReset(callback) {
    timeoutResetCallback = callback
  }

  return {
    collectAudioChunk,
    resetAudioChunks,
    saveAndPlayAudio,
    playAudio,
    stopPlay,
    pausePlay,
    resumePlay,
    closeAudio,
    onCompleted,
    onPlayStarted,
    onTimeoutReset,
  }
}
