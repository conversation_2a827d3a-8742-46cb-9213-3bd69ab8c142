import CryptoJS from 'crypto-js'

import { sleep } from '@/utils/index.js'

import {
  createAudioManager,
  createAuthUrlGenerator,
  createConfigManager,
  createDateStringGenerator,
  createMessageHandler,
  createWebSocketManager,
} from './helper.js'

export function useXfyunTTS(options = {}) {
  // 默认配置
  const defaultConfig = {
    appId: 'ee24dd0f', // 需要用户提供
    apiSecret: 'MjExYjUwNjkwNDM2NWE4YzM3NmVmZmY5', // 需要用户提供
    apiKey: '704e07c2dc9263dafa3f1f78210a0c25', // 需要用户提供
    // 语音参数
    vcn: 'x4_yezi', // 发音人
    speed: 50, // 语速 0-100
    volume: 50, // 音量 0-100
    pitch: 50, // 音高 0-100
    aue: 'lame', // 音频编码格式
    sfl: 1, // 流式返回 mp3
    auf: 'audio/L16;rate=16000', // 音频采样率
    tte: 'UTF8', // 文本编码
    reg: '0', // 英文发音方式
    rdn: '0', // 数字发音方式
    bgs: 0, // 背景音
    // 超时配置
    playbackTimeout: 30 * 1000, // 播放超时时间
    ...options,
  }

  // 状态管理
  const state = {
    isConnecting: false,
    isConnected: false,
    isSynthesizing: false,
    isPlaying: false,
    progress: 0,
    error: null,
    audioUrl: null,
  }

  // 播放队列管理
  const playQueue = []
  let isProcessingQueue = false
  let currentPlayingItem = null
  let playbackTimeoutId = null // 播放超时定时器
  let timeoutResetCount = 0 // 超时重置次数计数器
  const MAX_TIMEOUT_RESETS = 100 // 最大允许重置次数，按10秒间隔可支持约16分钟的播放

  // 队列项结构定义
  const createQueueItem = (text, config, priority = 'enqueue') => ({
    id: Date.now() + Math.random(),
    text,
    config,
    priority,
    timestamp: Date.now(),
    status: 'pending', // pending, playing, completed, failed
  })

  // 创建各个模块
  const configManager = createConfigManager(defaultConfig)
  const dateStringGenerator = createDateStringGenerator()
  const authUrlGenerator = createAuthUrlGenerator(dateStringGenerator)
  const audioManager = createAudioManager(state)
  const messageHandler = createMessageHandler(state, audioManager)
  const webSocketManager = createWebSocketManager(authUrlGenerator, messageHandler)

  /**
   * 处理播放完成（成功或失败）
   */
  async function handlePlaybackCompletion(isSuccess = true, error = null) {
    // 清除超时定时器
    if (playbackTimeoutId) {
      clearTimeout(playbackTimeoutId)
      playbackTimeoutId = null
    }

    // 重置超时重置计数器
    timeoutResetCount = 0

    // 只关闭WebSocket连接，不清空队列
    closeConnection()

    if (currentPlayingItem) {
      const config = currentPlayingItem.config

      if (isSuccess) {
        currentPlayingItem.status = 'completed'
        currentPlayingItem = null
        config?.onSuccess?.()
      }
      else {
        currentPlayingItem.status = 'failed'

        uni.showToast({
          title: `${currentPlayingItem.text}(语音播放失败)`,
          icon: 'none',
        })
        await sleep(2000)

        currentPlayingItem = null

        config?.onError?.(error || new Error('播放超时'))
      }

      config?.onCompleted?.(isSuccess, error)

      if (config?.toast) {
        uni.hideToast()
      }
    }

    // 标记当前项处理完成，允许处理下一项
    isProcessingQueue = false

    // 继续处理队列中的下一项
    processQueue()
  }

  audioManager.onCompleted(() => {
    handlePlaybackCompletion(true)
  })

  audioManager.onPlayStarted(() => {
    // 音频开始播放时的回调，超时定时器已在发送请求前启动
  })

  // 设置超时重置回调
  audioManager.onTimeoutReset(() => {
    resetTimeoutTimer()
  })

  // 设置消息处理错误回调
  messageHandler.setErrorCallback((error) => {
    console.error('TTS处理过程中发生错误:', error.message)
    handlePlaybackCompletion(false, error)
  })

  /**
   * 启动超时定时器
   */
  function startTimeoutTimer() {
    // 清除之前的定时器（如果存在）
    if (playbackTimeoutId) {
      clearTimeout(playbackTimeoutId)
    }

    // 重置计数器
    timeoutResetCount = 0

    const timeout = currentPlayingItem?.config?.playbackTimeout || defaultConfig.playbackTimeout

    playbackTimeoutId = setTimeout(() => {
      console.warn('TTS处理超时，强制标记为失败并继续下一项')
      handlePlaybackCompletion(false, new Error(`处理超时（${timeout}ms）`))
    }, timeout)

    console.log(`启动超时定时器，超时时间: ${timeout}ms`)
  }

  /**
   * 重置超时定时器（由音频播放进度触发）
   */
  function resetTimeoutTimer() {
    // 检查是否超过最大重置次数
    if (timeoutResetCount >= MAX_TIMEOUT_RESETS) {
      console.warn(`超时定时器重置次数已达上限 (${MAX_TIMEOUT_RESETS})，不再重置`)
      console.warn('这可能表示音频播放时间过长或存在异常，建议检查音频内容长度')
      return
    }

    // 只有在有活跃的超时定时器时才重置
    if (playbackTimeoutId && currentPlayingItem) {
      // 清除当前定时器
      clearTimeout(playbackTimeoutId)

      // 增加重置计数
      timeoutResetCount++

      const timeout = currentPlayingItem.config?.playbackTimeout || defaultConfig.playbackTimeout
      const estimatedPlayTime = timeoutResetCount * 10 // 按10秒间隔估算已播放时间

      // 重新启动定时器
      playbackTimeoutId = setTimeout(() => {
        console.warn('TTS处理超时，强制标记为失败并继续下一项')
        handlePlaybackCompletion(false, new Error(`处理超时（${timeout}ms）`))
      }, timeout)

      console.log(`重置超时定时器 (第${timeoutResetCount}次/${MAX_TIMEOUT_RESETS})，已播放约${estimatedPlayTime}秒，超时时间: ${timeout}ms`)
    }
  }

  /**
   * 处理播放队列
   */
  async function processQueue() {
    // 如果正在处理队列或队列为空，直接返回
    if (isProcessingQueue || playQueue.length === 0) {
      return
    }

    // 标记正在处理队列
    isProcessingQueue = true

    // 获取队列中的下一项
    const queueItem = playQueue.shift()
    currentPlayingItem = queueItem
    queueItem.status = 'playing'

    try {
      await performSynthesis(queueItem.text, queueItem.config)
      // 注意：不在这里设置 isProcessingQueue = false
      // 等待音频播放完成，由 audioManager.onCompleted 回调处理
    }
    catch (error) {
      console.error('TTS播放失败:', error)
      queueItem.status = 'failed'
      queueItem.config.onError?.(error)

      if (queueItem.config.toast) {
        uni.hideToast()
      }

      currentPlayingItem = null
      isProcessingQueue = false

      // 发生错误时，继续处理下一项
      processQueue()
    }
  }

  /**
   * 添加到播放队列
   */
  function addToQueue(text, config, priority = 'enqueue') {
    const queueItem = createQueueItem(text, config, priority)

    switch (priority) {
      case 'immediate':
        // 立即播放，插队到队列最前面
        playQueue.unshift(queueItem)
        break
      case 'replace':
        // 清空队列并立即播放
        clearQueue()
        playQueue.unshift(queueItem)
        break
      case 'enqueue':
      default:
        // 加入队列末尾
        playQueue.push(queueItem)
        break
    }

    // 开始处理队列
    if (!isProcessingQueue) {
      processQueue()
    }

    return queueItem
  }

  /**
   * 执行语音合成（内部方法）
   */
  async function performSynthesis(text, config) {
    if (!text || text.trim() === '') {
      throw new Error('文本内容不能为空')
    }

    const finalConfig = { ...configManager.getConfig(), ...config }

    // 验证必要参数
    if (!finalConfig.appId || !finalConfig.apiKey || !finalConfig.apiSecret) {
      throw new Error('缺少必要的认证参数(appId, apiKey, apiSecret)')
    }

    if (finalConfig.toast) {
      uni.showToast({
        title: text,
        icon: 'none',
        duration: 60 * 1000,
      })
    }

    try {
      // 重置状态
      state.error = null
      state.progress = 0
      audioManager.resetAudioChunks()

      // 如果已连接，直接发送请求
      if (state.isConnected) {
        state.isSynthesizing = true
        sendSynthesisRequest(text, finalConfig)
        return
      }

      // 创建新连接
      state.isConnecting = true
      await webSocketManager.createWebSocket(finalConfig)

      // 发送合成请求
      state.isSynthesizing = true
      sendSynthesisRequest(text, finalConfig)
    }
    catch (error) {
      console.error('语音合成失败:', error)
      state.error = '语音合成失败'
      state.isSynthesizing = false

      // 清除超时定时器
      if (playbackTimeoutId) {
        clearTimeout(playbackTimeoutId)
        playbackTimeoutId = null
      }

      throw error
    }
  }

  /**
   * 发送合成请求
   */
  function sendSynthesisRequest(text, config) {
    // 在发送请求前启动超时定时器
    startTimeoutTimer()

    const requestData = {
      common: {
        app_id: config.appId,
      },
      business: {
        aue: config.aue,
        sfl: config.sfl,
        auf: config.auf,
        vcn: config.vcn,
        speed: config.speed,
        volume: config.volume,
        pitch: config.pitch,
        bgs: config.bgs,
        tte: config.tte,
        reg: config.reg,
        rdn: config.rdn,
      },
      data: {
        status: 2,
        text: CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(text)),
      },
    }

    webSocketManager.sendMessage(requestData)
  }

  /**
   * 清空播放队列
   */
  function clearQueue() {
    // 清除超时定时器
    if (playbackTimeoutId) {
      clearTimeout(playbackTimeoutId)
      playbackTimeoutId = null
    }

    // 重置超时重置计数器
    timeoutResetCount = 0

    // 停止当前播放
    if (currentPlayingItem) {
      audioManager.stopPlay()
      currentPlayingItem.status = 'cancelled'
      currentPlayingItem.config.onError?.(new Error('播放被取消'))
      currentPlayingItem = null
    }

    // 将队列中所有待处理项标记为取消
    playQueue.forEach((item) => {
      item.status = 'cancelled'
      item.config.onError?.(new Error('播放被取消'))
    })

    // 清空队列
    playQueue.length = 0
    isProcessingQueue = false

    // 隐藏toast
    uni.hideToast()
  }

  /**
   * 获取当前队列状态
   */
  function getQueue() {
    return {
      queue: [...playQueue],
      currentPlaying: currentPlayingItem,
      isProcessing: isProcessingQueue,
      queueLength: playQueue.length,
    }
  }

  /**
   * 重置队列并播放新语音
   */
  function resetQueueAndPlay(text, customConfig = {}) {
    return speak(text, { ...customConfig, priority: 'replace' })
  }

  /**
   * 语音合成主函数（支持队列和优先级）
   */
  function speak(text, customConfig = {}) {
    if (!text || text.trim() === '') {
      state.error = '文本内容不能为空'
      return null
    }

    const config = { ...customConfig }
    const priority = config.priority || 'enqueue'

    // 移除priority字段，避免传递给底层合成函数
    delete config.priority

    // 验证必要参数
    const finalConfig = { ...configManager.getConfig(), ...config }
    if (!finalConfig.appId || !finalConfig.apiKey || !finalConfig.apiSecret) {
      state.error = '缺少必要的认证参数(appId, apiKey, apiSecret)'
      return null
    }

    // 添加到队列
    return addToQueue(text, config, priority)
  }

  /**
   * 关闭WebSocket连接（内部方法，不清空队列）
   */
  function closeConnection() {
    // 清除超时定时器
    if (playbackTimeoutId) {
      clearTimeout(playbackTimeoutId)
      playbackTimeoutId = null
    }

    // 重置超时重置计数器
    timeoutResetCount = 0

    webSocketManager.closeWebSocket()
    audioManager.closeAudio()
    state.isConnected = false
    state.isConnecting = false
    state.isSynthesizing = false
    state.isPlaying = false
  }

  /**
   * 关闭连接并清空队列（外部调用）
   */
  function close() {
    // 清空队列
    clearQueue()

    // 关闭连接
    closeConnection()
  }

  return {
    // 状态
    state,

    // 基础方法
    speak,
    stopPlay: audioManager.stopPlay,
    pausePlay: audioManager.pausePlay,
    resumePlay: audioManager.resumePlay,
    setConfig: configManager.setConfig,
    close,

    // 队列管理方法
    clearQueue,
    getQueue,
    resetQueueAndPlay,
  }
}
