import { throttle } from 'es-toolkit'

export function useScrollView({ scrollType = 'x', scrollDistance = 10, scrollOffset = 0, scrollIndex, scrollIntoView, throttleMs = 1000 } = {}) {
  const scrollLeft = ref(0)
  const scrollTop = ref(0)
  const scrollWidth = ref(0)
  const scrollHeight = ref(0)

  watchEffect(() => {
    if (scrollIndex?.value === void 0) {
      return false
    }

    if (scrollType === 'x') {
      scrollLeft.value = (scrollDistance * scrollIndex.value) - scrollOffset
    }
    else if (scrollType === 'y') {
      scrollTop.value = (scrollDistance * scrollIndex.value) - scrollOffset
    }
  })

  function onScroll({ detail }) {
    scrollLeft.value = detail.scrollLeft
    scrollTop.value = detail.scrollTop
    scrollWidth.value = detail.scrollWidth
    scrollHeight.value = detail.scrollHeight
  }

  async function scrollX(forward = true) {
    await nextTick()

    if (forward) {
      scrollLeft.value += scrollDistance
    }
    else {
      scrollLeft.value -= scrollDistance
    }
  }

  async function scrollY(forward = true) {
    await nextTick()

    if (forward) {
      scrollTop.value += scrollDistance
    }
    else {
      scrollTop.value -= scrollDistance
    }
  }

  let scrollIntoViewId = 'scroll-into-view'

  watchEffect(() => {
    if (scrollIntoView?.value) {
      scrollIntoViewId = scrollIntoView.value
    }
  })

  const scrollToBottom = throttle(async () => {
    scrollIntoView.value = ''

    await nextTick()

    scrollIntoView.value = scrollIntoViewId
  }, throttleMs)

  return {
    onScroll,
    scrollLeft,
    scrollTop,
    scrollX,
    scrollY,
    scrollToBottom,
  }
}

export default useScrollView
