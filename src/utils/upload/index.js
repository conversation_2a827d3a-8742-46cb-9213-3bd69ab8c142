import { postUpload } from '@/api/common/index.js'

/**
 * 批量上传文件
 * @param {Array} tempFilePaths - 临时文件路径数组
 * @param {object} options - 配置选项
 * @param {boolean} options.showProgress - 是否显示上传进度，默认 false
 * @param {boolean} options.concurrent - 是否并发上传，默认 false（顺序上传）
 * @param {string} options.loadingTitle - 自定义 loading 标题
 * @returns {Promise<Array|false>} 返回上传成功的文件URL数组，失败返回 false
 *
 * @example
 * // 基础用法
 * const urls = await batchUpload(tempFilePaths)
 *
 * // 显示进度（顺序上传，可看到准确进度）
 * const urls = await batchUpload(tempFilePaths, { showProgress: true })
 *
 * // 并发上传（速度更快，但进度不够精确）
 * const urls = await batchUpload(tempFilePaths, { showProgress: true, concurrent: true })
 *
 * // 自定义 loading 标题
 * const urls = await batchUpload(tempFilePaths, {
 *   showProgress: true,
 *   loadingTitle: '正在处理图片...'
 * })
 */
export async function batchUpload(tempFilePaths, options) {
  const {
    showProgress = false,
    concurrent = false,
    loadingTitle = '上传中',
  } = options || {}

  if (showProgress) {
    uni.showLoading({
      title: tempFilePaths.length > 1
        ? `准备上传 ${tempFilePaths.length} 个文件...`
        : loadingTitle,
    })
  }

  try {
    let results

    if (concurrent) {
      // 并发上传模式
      const promises = tempFilePaths.map(async (filePath) => {
        return await postUpload({
          name: 'file',
          filePath,
        })
      })

      results = await Promise.allSettled(promises)
    }
    else {
      // 顺序上传模式，可以显示准确的进度
      results = []

      for (let index = 0; index < tempFilePaths.length; index++) {
        const filePath = tempFilePaths[index]

        if (showProgress) {
          uni.showLoading({
            title: tempFilePaths.length > 1
              ? `${loadingTitle} ${index + 1}/${tempFilePaths.length}`
              : loadingTitle,
          })
        }

        try {
          const res = await postUpload({
            name: 'file',
            filePath,
          })
          results.push({ status: 'fulfilled', value: res })
        }
        catch (error) {
          results.push({ status: 'rejected', reason: error })
        }
      }
    }

    const remoteFilePaths = results.reduce((arr, item) => {
      const url = item.value?.data?.url

      if (url) {
        arr.push(url)
      }

      return arr
    }, [])

    if (!remoteFilePaths.length) {
      uni.showToast({
        title: '上传文件失败',
        icon: 'none',
      })
      return false
    }

    return remoteFilePaths
  }
  catch (error) {
    console.error('批量上传失败:', error)
    uni.showToast({
      title: '上传过程中出现错误',
      icon: 'none',
    })
    return false
  }
  finally {
    if (showProgress) {
      uni.hideLoading()
    }
  }
}

export function parseRemoteFilePaths(fileList, { responseKey = 'response' } = {}) {
  const value = fileList.reduce((arr, item) => {
    let response

    try {
      response = JSON.parse(item[responseKey])
    }
    catch (error) {
      console.warn(error)
    }

    if (response?.data?.url) {
      arr.push(response?.data?.url)
    }

    return arr
  }, [])

  return value
}
