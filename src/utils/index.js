/**
 * @desc 使用async await 进项进行延时操作
 * @param {*} time
 */
export function sleep(time = 500) {
  return new Promise((resolve) => {
    setTimeout(() => resolve(true), time)
  })
}

export function generateLiveUrl(path) {
  const value = `${process.env.VITE_LIVE_PUSHER_URL}${path}?user=admin&pass=admin123`
  return value
}

/**
 * 设置查询的存储数据
 * @param {*} value
 * @returns
 */
export function setStoreQuery(value) {
  const key = String(Date.now())

  uni.setStorageSync(key, value)

  return key
}

/**
 * 获取存储查询的数据
 * @param {*} key
 * @param {*} remove 是否移除存储的数据
 * @returns {any}
 */
export function getStoreQuery(key, remove = true) {
  const value = uni.getStorageSync(key)

  if (remove) {
    uni.removeStorageSync(key)
  }

  return value
}

export function numberToChinese(num) {
  if (typeof num !== 'number' || num < 0 || num > 9999 || !Number.isInteger(num)) {
    throw new Error('只支持 0 到 9999 的整数')
  }

  const cnNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const cnUnits = ['', '十', '百', '千']
  let result = ''
  const digits = String(num).split('').reverse()

  let zeroFlag = false
  for (let i = digits.length - 1; i >= 0; i--) {
    const digit = Number.parseInt(digits[i])
    if (digit === 0) {
      if (!zeroFlag && result !== '') {
        result += cnNums[0]
        zeroFlag = true
      }
    }
    else {
      result += cnNums[digit] + cnUnits[i]
      zeroFlag = false
    }
  }

  // 特殊处理 10~19
  if (num >= 10 && num < 20) {
    result = result.replace(/^一十/, '十')
  }

  // 去除末尾的“零”
  result = result.replace(/零+$/, '')

  return result || cnNums[0]
}

/** 成双工具展示格式化 */
export function doubleFormatter(name, num) {
  const value = num || 0

  if (!['绝缘靴', '绝缘手套'].includes(name) || !value) {
    return value
  }

  return `${value}(${Math.round(value / 2)}套)`
}

/** 校验是否为 AR 眼镜设备 */
export function isGlassesDevice() {
  const systemInfo = uni.getSystemInfoSync()
  return ['AS001'].includes(systemInfo?.model)
}
