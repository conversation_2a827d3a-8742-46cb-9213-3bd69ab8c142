// EventStream 处理工具类
class EventStreamHandler {
  constructor(url, options = {}) {
    this.url = url
    this.options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        ...options.headers,
      },
      body: JSON.stringify(options.data || {}),
      ...options,
    }
    this.onMessage = options.onMessage || (() => {})
    this.onError = options.onError || (() => {})
    this.onComplete = options.onComplete || (() => {})
    this.controller = null
    this.isRunning = false
  }

  // 检测运行环境
  isApp() {
    // #ifdef APP-PLUS
    return true
    // #endif
    // #ifndef APP-PLUS
    // eslint-disable-next-line no-unreachable
    return false
    // #endif
  }

  // 浏览器端使用 fetch + ReadableStream
  async startBrowserStream() {
    try {
      this.controller = new AbortController()
      const response = await fetch(this.url, {
        ...this.options,
        signal: this.controller.signal,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      this.isRunning = true

      while (this.isRunning) {
        const { done, value } = await reader.read()

        if (done) {
          this.handleComplete()
          break
        }

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // 保留不完整的行

        for (const line of lines) {
          this.processLine(line.trim())
        }
      }
    }
    catch (error) {
      if (error.name !== 'AbortError') {
        this.handleError(error)
      }
    }
  }

  // App端使用 plus.net.XMLHttpRequest
  startAppStream() {
    if (!plus) {
      console.warn('plus variable is not declared')
      return
    }

    // #ifdef APP-PLUS
    const xhr = new plus.net.XMLHttpRequest()
    let lastResponseLength = 0

    xhr.onreadystatechange = () => {
      if (xhr.readyState === 3 || xhr.readyState === 4) {
        const responseText = xhr.responseText || ''
        const newData = responseText.substring(lastResponseLength)
        lastResponseLength = responseText.length

        if (newData) {
          const lines = newData.split('\n')
          for (const line of lines) {
            this.processLine(line.trim())
          }
        }

        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            this.handleComplete()
          }
          else {
            this.handleError(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`))
          }
        }
      }
    }

    xhr.onerror = () => {
      this.handleError(new Error('Network request failed'))
    }

    try {
      xhr.open(this.options.method, this.url, true)

      // 设置请求头
      Object.keys(this.options.headers).forEach((key) => {
        xhr.setRequestHeader(key, this.options.headers[key])
      })

      this.isRunning = true
      xhr.send(this.options.body)

      // 保存xhr引用用于取消请求
      this.xhr = xhr
    }
    catch (error) {
      this.handleError(error)
    }
    // #endif
  }

  // 处理单行数据
  processLine(line) {
    if (!line || !this.isRunning)
      return

    const dataPrefix = 'data:'

    if (line.startsWith(dataPrefix)) {
      const data = line.substring(dataPrefix.length).trim()
      // 检查是否为结束标志
      if (data === 'true' || data === '[DONE]') {
        this.handleComplete()
        return
      }

      try {
        const jsonData = JSON.parse(data)
        this.handleMessage(jsonData)
      }
      catch (error) {
        console.warn('Failed to parse JSON:', data, error)
      }
    }
  }

  // 处理消息
  handleMessage(data) {
    if (typeof this.onMessage === 'function') {
      this.onMessage(data)
    }
  }

  // 处理错误
  handleError(error) {
    this.isRunning = false
    if (typeof this.onError === 'function') {
      this.onError(error)
    }
  }

  // 处理完成
  handleComplete() {
    this.isRunning = false
    if (typeof this.onComplete === 'function') {
      this.onComplete()
    }
  }

  // 开始流式请求
  start() {
    if (this.isRunning) {
      console.warn('Stream is already running')
      return
    }

    if (this.isApp()) {
      this.startAppStream()
    }
    else {
      this.startBrowserStream()
    }
  }

  // 停止流式请求
  stop() {
    this.isRunning = false

    if (this.controller) {
      this.controller.abort()
      this.controller = null
    }

    // #ifdef APP-PLUS
    if (this.xhr) {
      this.xhr.abort()
      this.xhr = null
    }
    // #endif
  }
}

export default EventStreamHandler
