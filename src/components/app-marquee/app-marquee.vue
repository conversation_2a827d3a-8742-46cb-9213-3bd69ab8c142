<script setup lang="ts">
import { onHide, onShow } from '@dcloudio/uni-app'
import { computed, onMounted, ref, watch } from 'vue'

// --- Props ---
const props = defineProps({
  // 滚动的列表数据
  list: {
    type: Array as () => any[],
    default: () => [],
  },
  // 滚动速度，单位 px/s
  speed: {
    type: Number,
    default: 50,
  },
})

// --- Emits ---
const emit = defineEmits(['click'])

// --- State ---
const scrollLeft = ref(0) // 滚动的距离
const contentWidth = ref(0) // 内容区域总宽度（单次）
const containerWidth = ref(0) // scroll-view 容器宽度
const timer = ref<NodeJS.Timeout | null>(null) // 定时器

// --- Computed ---
// 计算单次滚动完成所需的动画时间
const animationDuration = computed(() => {
  if (contentWidth.value === 0 || props.speed === 0) {
    return 0
  }
  return contentWidth.value / props.speed
})

// --- Methods ---
/**
 * @description 初始化组件尺寸
 */
function init() {
  const query = uni.createSelectorQuery().in(this)
  query.select('.marquee-container').boundingClientRect((containerRes) => {
    if (containerRes) {
      containerWidth.value = containerRes.width
      query.select('.marquee-item').boundingClientRect((itemRes) => {
        if (itemRes && props.list.length > 0) {
          contentWidth.value = itemRes.width * props.list.length
          // 如果内容宽度小于容器宽度，则不滚动
          if (contentWidth.value <= containerWidth.value) {
            stopScroll()
          }
          else {
            startScroll()
          }
        }
      }).exec()
    }
  }).exec()
}

/**
 * @description 开始滚动
 */
function startScroll() {
  // 如果已经有定时器，先清除
  if (timer.value) {
    stopScroll()
  }

  const scroll = () => {
    // 每次移动 1px
    scrollLeft.value += 1
    // 当滚动到第一部分内容的末尾时，瞬间回到起点，实现无缝连接
    if (scrollLeft.value >= contentWidth.value) {
      scrollLeft.value = 0
    }
  }

  // 根据速度设置定时器
  const interval = 1000 / props.speed
  timer.value = setInterval(scroll, interval)
}

/**
 * @description 停止滚动
 */
function stopScroll() {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

/**
 * @description 点击事件
 */
function handleClick() {
  emit('click')
}

// --- Lifecycle Hooks ---
onMounted(() => {
  // 需要延迟一点时间获取正确的宽度
  setTimeout(() => {
    init()
  }, 100)
})

// 页面显示时，如果内容宽度大于容器，则开始滚动
onShow(() => {
  if (contentWidth.value > containerWidth.value) {
    startScroll()
  }
})

// 页面隐藏时，停止滚动，节省资源
onHide(() => {
  stopScroll()
})

// 监听列表数据变化，重新初始化
watch(() => props.list, () => {
  // 数据变化后，DOM更新需要时间
  setTimeout(() => {
    init()
  }, 100)
}, { deep: true })
</script>

<template>
  <view class="marquee-container" @click="handleClick">
    <scroll-view
      class="marquee-scroll-view"
      :scroll-x="true"
      :show-scrollbar="false"
      :scroll-left="scrollLeft"
      @scroll="handleScroll"
    >
      <view class="marquee-content" :style="{ animationDuration: `${animationDuration}s` }">
        <view v-for="(item, index) in list" :key="`front-${index}`" class="marquee-item">
          <slot :item="item" :index="index">
            {{ item }}
          </slot>
        </view>
        <view v-for="(item, index) in list" :key="`back-${index}`" class="marquee-item">
          <slot :item="item" :index="index">
            {{ item }}
          </slot>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.marquee-container {
  width: 100%;
  overflow: hidden;
  height: 100%; // 确保容器有高度
}

.marquee-scroll-view {
  width: 100%;
  height: 100%;

  // 隐藏H5平台的滚动条
  /* #ifdef H5 */
  ::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }
  /* #endif */
}

.marquee-content {
  display: flex;
  flex-wrap: nowrap; // 保证不换行
  white-space: nowrap;
}
</style>
