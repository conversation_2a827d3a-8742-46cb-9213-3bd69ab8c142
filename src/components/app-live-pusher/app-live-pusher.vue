<script>
import { generateLiveUrl, sleep } from '@/utils/index.js'

export default {
  props: {
    customClass: {
      type: [String, Object, Array],
      default: '',
    },
    debug: {
      type: Boolean,
      default: process.env.VITE_NODE_ENV === 'dev',
    },
    path: {
      type: String,
      default: '',
    },
    mode: {
      type: String,
      default: 'HD',
      description: '推流视频模式，可取值：SD（标清）, HD（高清）, FHD（超清）',
    },
    aspect: {
      type: String,
      default: '16:9',
      description: '视频宽高比例',
    },
    muted: {
      type: Boolean,
      default: false,
      description: '是否静音',
    },
    enableCamera: {
      type: Boolean,
      default: true,
      description: '开启摄像头',
    },
    autoFocus: {
      type: Boolean,
      default: true,
      description: '自动聚焦',
    },
    beauty: {
      type: Number,
      default: 0,
      description: '美颜，取值范围 0-9（iOS取值范围为1），0 表示关闭',
    },
    whiteness: {
      type: Number,
      default: 0,
      description: '美白，取值范围 0-9（iOS取值范围为1），0 表示关闭',
    },
    orientation: {
      type: String,
      default: 'vertical',
      description: '画面方向 horizontal 或 vertical',
    },
    minBitrate: {
      type: Number,
      default: 200,
      description: '最小码率',
    },
    maxBitrate: {
      type: Number,
      default: 1000,
      description: '最大码率',
    },
    audioQuality: {
      type: String,
      default: 'high',
      description: '高音质(48KHz)或低音质(16KHz)，值为 high 或 low',
    },
    waitingImage: {
      type: String,
      default: '',
      description: '进入后台时推流的等待画面',
    },
    waitingImageHash: {
      type: String,
      default: '',
      description: '等待画面资源的 MD5 值',
    },
    zoom: {
      type: Boolean,
      default: true,
      description: '调整焦距',
    },
    devicePosition: {
      type: String,
      default: 'back',
      description: '前置或后置，值为 front, back',
    },
    backgroundMute: {
      type: Boolean,
      default: false,
      description: '进入后台时是否静音',
    },
    remoteMirror: {
      type: Boolean,
      default: true,
      description: '设置推流画面是否镜像（影响 live-player）',
    },
    localMirror: {
      type: String,
      default: 'auto',
      description: '控制本地预览画面是否镜像',
    },
    audioReverbType: {
      type: Number,
      default: 0,
      description: '音频混响类型',
    },
    enableMic: {
      type: Boolean,
      default: true,
      description: '开启或关闭麦克风',
    },
    enableAgc: {
      type: Boolean,
      default: false,
      description: '是否开启音频自动增益',
    },
    enableAns: {
      type: Boolean,
      default: false,
      description: '是否开启音频噪声抑制',
    },
    audioVolumeType: {
      type: String,
      default: 'voicecall',
      description: '音量类型',
    },
    pushing: {
      type: Boolean,
      default: false,
    },
    reconnection: {
      type: Boolean,
      default: true,
    },
  },

  emits: ['register', 'onStateChange', 'onNetStatus', 'onError', 'onBgmStart', 'onBgmProgress', 'onBgmComplete', 'update:pushing', 'success'],

  computed: {
    url() {
      const value = generateLiveUrl(this.$props.path)
      return value
    },
  },
  methods: {
    init(componentInstance) {
      this.context = uni.createLivePusherContext('livePusher', componentInstance)
      this.$emit('register', this)
      return this
    },
    start() {
      return new Promise((resolve, reject) => {
        this.context.start({
          success: (...args) => {
            if (this.debug) {
              console.log(`livePusher.start.success:${JSON.stringify(args)}`)
            }

            resolve(...args)
          },
          fail: (...args) => {
            if (this.debug) {
              console.log(`livePusher.start.fail:${JSON.stringify(args)}`)
            }

            if (this.reconnection) {
              clearTimeout(this.startTimer)
              this.startTimer = setTimeout(() => {
                this.restart()
              }, 2000)
            }

            reject(args)
          },
        })
      })
    },
    async restart() {
      this.stop()
      await sleep(1000)
      this.startPreview()
      this.start()
    },
    close() {
      return new Promise((resolve, reject) => {
        this.context.close({
          success: (...args) => {
            if (this.debug) {
              console.log(`livePusher.close.success:${JSON.stringify(args)}`)
            }

            resolve(...args)
          },
          fail: (...args) => {
            if (this.debug) {
              console.log(`livePusher.close.fail:${JSON.stringify(args)}`)
            }

            reject(args)
          },
        })
      })
    },
    snapshot() {
      return new Promise((resolve, reject) => {
        this.context.snapshot({
          success: (...args) => {
            if (this.debug) {
              console.log(`livePusher.snapshot.success:${JSON.stringify(args)}`)
            }

            resolve(...args)
          },
          fail: (...args) => {
            if (this.debug) {
              console.log(`livePusher.snapshot.fail:${JSON.stringify(args)}`)
            }

            reject(args)
          },
        })
      })
    },
    resume() {
      return new Promise((resolve, reject) => {
        this.context.resume({
          success: (...args) => {
            if (this.debug) {
              console.log(`livePusher.resume.success:${JSON.stringify(args)}`)
            }

            resolve(...args)
          },
          fail: (...args) => {
            if (this.debug) {
              console.log(`livePusher.resume.fail:${JSON.stringify(args)}`)
            }

            reject(args)
          },
        })
      })
    },
    pause() {
      return new Promise((resolve, reject) => {
        this.context.pause({
          success: (...args) => {
            if (this.debug) {
              console.log(`livePusher.pause.success:${JSON.stringify(args)}`)
            }

            resolve(...args)
          },
          fail: (...args) => {
            if (this.debug) {
              console.log(`livePusher.pause.fail:${JSON.stringify(args)}`)
            }

            reject(args)
          },
        })
      })
    },
    stop() {
      return new Promise((resolve, reject) => {
        this.context.stop({
          success: (...args) => {
            if (this.debug) {
              console.log(`livePusher.stop.success:${JSON.stringify(args)}`)
            }

            resolve(...args)
          },
          fail: (...args) => {
            if (this.debug) {
              console.log(`livePusher.stop.fail:${JSON.stringify(args)}`)
            }

            reject(args)
          },
        })
      })
    },
    switchCamera() {
      return new Promise((resolve, reject) => {
        this.context.switchCamera({
          success: (...args) => {
            if (this.debug) {
              console.log(`livePusher.switchCamera.success:${JSON.stringify(args)}`)
            }

            resolve(...args)
          },
          fail: (...args) => {
            if (this.debug) {
              console.log(`livePusher.switchCamera.fail:${JSON.stringify(args)}`)
            }

            reject(args)
          },
        })
      })
    },
    startPreview() {
      return new Promise((resolve, reject) => {
        this.context.startPreview({
          success: (...args) => {
            if (this.debug) {
              console.log(`livePusher.startPreview.success:${JSON.stringify(args)}`)
            }

            resolve(...args)
          },
          fail: (...args) => {
            if (this.debug) {
              console.log(`livePusher.startPreview.fail:${JSON.stringify(args)}`)
            }

            reject(args)
          },
        })
      })
    },
    stopPreview() {
      return new Promise((resolve, reject) => {
        this.context.stopPreview({
          success: (...args) => {
            if (this.debug) {
              console.log(`livePusher.stopPreview.success:${JSON.stringify(args)}`)
            }

            resolve(...args)
          },
          fail: (...args) => {
            if (this.debug) {
              console.log(`livePusher.stopPreview.fail:${JSON.stringify(args)}`)
            }

            reject(args)
          },
        })
      })
    },

    async onStateChange(...args) {
      if (this.debug) {
        console.log(`livePusher.onStateChange: ${JSON.stringify(args)}`)
      }

      const code = args[0]?.detail?.code

      if ([1002].includes(code)) {
        console.log('onStateChange.url', this.url)
        this.$emit('update:pushing', true)
        this.$emit('success', args[0].detail)
      }
      else if (code < 0 || code > 3000) {
        this.$emit('update:pushing', false)
        if (this.reconnection) {
          clearTimeout(this.startTimer)
          this.startTimer = setTimeout(() => {
            this.restart()
          }, 2000)
        }
      }

      this.$emit('onStateChange', ...args)
    },
    onNetStatus(...args) {
      this.netStatus = args[0]?.detail?.info

      this.$emit('onNetStatus', ...args)
    },
    onError(...args) {
      if (this.debug) {
        console.log(`livePusher.onError: ${JSON.stringify(args)}`)
      }

      this.$emit('onError', ...args)
    },
    onBgmStart(...args) {
      if (this.debug) {
        console.log(`livePusher.onBgmStart: ${JSON.stringify(args)}`)
      }

      this.$emit('onBgmStart', ...args)
    },
    onBgmProgress(...args) {
      if (this.debug) {
        console.log(`livePusher.onBgmProgress: ${JSON.stringify(args)}`)
      }

      this.$emit('onBgmProgress', ...args)
    },
    onBgmComplete(...args) {
      if (this.debug) {
        console.log(`livePusher.onBgmComplete: ${JSON.stringify(args)}`)
      }

      this.$emit('onBgmComplete', ...args)
    },
    onPusherViewClick() {
      let content = `地址: ${this.url};`

      if (this.netStatus) {
        content += `\n状态: ${JSON.stringify(this.netStatus)};`
      }

      console.warn('onPusherViewClick.content', content)

      uni.showModal({
        title: '推流信息',
        content,
        showCancel: false,
      })
    },
  },
}
</script>

<template>
  <view class="app-live-pusher" :class="[customClass]">
    <live-pusher
      id="livePusher"
      class="app-live-pusher__core"
      :url="url"
      :mode="mode"
      :aspect="aspect"
      :muted="muted"
      :enable-camera="enableCamera"
      :auto-focus="autoFocus"
      :beauty="beauty"
      :whiteness="whiteness"
      :orientation="orientation"
      :min-bitrate="minBitrate"
      :max-bitrate="maxBitrate"
      :audio-quality="audioQuality"
      :waiting-image="waitingImage"
      :waiting-image-hash="waitingImageHash"
      :zoom="zoom"
      :device-position="devicePosition"
      :background-mute="backgroundMute"
      :remote-mirror="remoteMirror"
      :local-mirror="localMirror"
      :audio-reverb-type="audioReverbType"
      :enable-mic="enableMic"
      :enable-agc="enableAgc"
      :enable-ans="enableAns"
      :audio-volume-type="audioVolumeType"
      @statechange="onStateChange"
      @netstatus="onNetStatus"
      @error="onError"
      @bgmstart="onBgmStart"
      @bgmprogress="onBgmProgress"
      @bgmcomplete="onBgmComplete"
    >
    </live-pusher>
  </view>
  <view class="app-live-pusher__debug" aria-label="推流信息" @click="onPusherViewClick"></view>
</template>

<style>
.app-live-pusher,
.app-live-pusher__core {
  flex: 1;
}

.app-live-pusher__debug {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 1px;
  height: 1px;
}
</style>
