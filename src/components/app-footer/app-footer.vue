<script setup>
import { isGlassesDevice } from '@/utils/index.js'

const props = defineProps({
  prompts: {
    type: Array,
    default: () => [],
  },
  hiddenBackButton: {
    type: Boolean,
    default: false,
  },
  hiddenHomeButton: {
    type: Boolean,
    default: false,
  },
  onBackClick: {
    type: Function,
    default: void 0,
  },
  onHomeClick: {
    type: Function,
    default: void 0,
  },
})

const isGlasses = isGlassesDevice()

const filterPrompts = computed(() => props.prompts.filter((item) => {
  if (item.glassesOnly) {
    return isGlasses
  }

  return true
}))

function onBackClick() {
  if (!props.onBackClick) {
    uni.navigateBack()
    return false
  }

  props.onBackClick?.()
}

function onHomeClick() {
  if (!props.onHomeClick) {
    uni.reLaunch({ url: '/pages/index/index' })
    return false
  }

  props.onHomeClick?.()
}

function onActionClick(item) {
  if (typeof item.action === 'function') {
    item?.action?.()
    return false
  }
}
</script>

<template>
  <view class="w-full flex text-slate-400 bg-[#0E0F12] py-[8rpx]" :class="isGlasses ? 'h-6' : 'h-8'">
    <view v-if="!hiddenBackButton" class="w-14 flex flex-none items-center justify-center border-r-[0.5rpx] border-gray-800" aria-label="返回" @click="onBackClick">
      <text class="text-[26rpx] text-slate-400">
        返回
      </text>
    </view>

    <view class="w-full flex flex-1 items-center justify-center overflow-hidden bg-gradient-to-r from-[#3A8DF7] to-[#3ACEF7] text-transparent bg-clip-text">
      <template v-if="prompts.length">
        <image v-if="isGlasses" class="size-[28rpx] flex-none" src="~@assets/images/index/carbon-microphone.png"></image>
        <view class="flex items-center justify-start truncate space-x-1">
          <text v-if="isGlasses" key="speak" class="inline flex-none text-[26rpx]/[26rpx]">
            请说
          </text>
          <slot>
            <text v-for="(item, index) of filterPrompts" :key="index" :aria-label="item.action ? item.label : ''"
              class="inline flex-none text-[26rpx]/[26rpx]" 
              :class="[
                {
                  [`rounded-sm px-2 py-[8rpx] ${['primary'].includes(item.type) || index === 0 ? 'bg-[#3A8DF7] text-white' : 'bg-[#282E3B] text-[#a9abb1]'}`]: !isGlasses,
                }
              ]"
              @click="onActionClick(item)">
              {{ isGlasses ? `"${item.label}"` : item.label }}
            </text>
          </slot>
        </view>
      </template>
    </view>

    <view v-if="!hiddenHomeButton" class="w-14 flex flex-none items-center justify-center border-l-[0.5rpx] border-gray-800" aria-label="首页" @click="onHomeClick">
      <text  class="text-[26rpx] text-slate-400">
        首页
      </text>
    </view>
  </view>
</template>