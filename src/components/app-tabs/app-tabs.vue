<script setup>
defineProps({
  model: {
    type: Array,
    default: () => [],
  },
  customClass: {
    type: String,
    default: '',
  },
})

const activeIndex = ref(0)

const { scrollLeft } = useScrollView({
  scrollIndex: activeIndex,
  scrollDistance: 50,
})

const modelValue = defineModel({
  type: [Number, String],
  default: '',
})

function isActive(item, index) {
  const value = item.value === modelValue.value || index === modelValue.value
  return value
}

function onTabClick(item, index) {
  activeIndex.value = index
  modelValue.value = item.value !== void 0 ? item.value : index
}
</script>

<template>
  <scroll-view class="w-full" scroll-x scroll-with-animation :scroll-left="scrollLeft" :class="[customClass]">
    <view class="flex w-full space-x-1">
      <view v-for="(item, index) of model" :key="index" :aria-label="item.label" class="relative flex-1 w-0 min-w-[fit-content] flex flex-col items-center justify-center" @click="onTabClick(item, index)">
        <view class="text-2xs relative truncate text-center w-full" :class="isActive(item, index) ? ['text-primary-500 font-bold'] : ['text-gray-50']">
          {{ item.label }}

          <view v-if="item.badge" class="text-2xs text-gray-50 absolute inset-center-y -right-1 translate-x-full bg-primary-500/25 text-primary-500 px-1 rounded-full">
            {{ item.badge > 99 ? '99+' : item.badge }}
          </view>
        </view>

        <view class="w-5 h-[4rpx] rounded-full mt-[2px]" :class="isActive(item, index) ? ['bg-primary-500'] : []"></view>
      </view>
    </view>
  </scroll-view>
</template>
