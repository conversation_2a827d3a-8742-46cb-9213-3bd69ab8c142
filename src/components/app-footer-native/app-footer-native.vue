<script setup>
import { isGlassesDevice } from '@/utils/index.js'

const props = defineProps({
  prompts: {
    type: Array,
    default: () => [],
  },
  hiddenBackButton: {
    type: Boolean,
    default: false,
  },
  hiddenHomeButton: {
    type: <PERSON>olean,
    default: false,
  },
  onBackClick: {
    type: Function,
    default: void 0,
  },
  onHomeClick: {
    type: Function,
    default: void 0,
  },
})

const isGlasses = isGlassesDevice()

function onBackClick() {
  if (!props.onBackClick) {
    uni.navigateBack()
    return false
  }

  props.onBackClick?.()
}

function onHomeClick() {
  if (!props.onHomeClick) {
    uni.reLaunch({ url: '/pages/index/index' })
    return false
  }

  props.onHomeClick?.()
}

function onActionClick(item) {
  if (typeof item.action === 'function') {
    item?.action?.()
    return false
  }
}
</script>

<template>
  <view class="app-footer-native" :class="[{ 'app-footer-native--enhanced': !isGlasses }]">
    <view v-if="!hiddenBackButton" class="app-footer-native__button-wrapper app-footer-native__button-wrapper--left">
      <text
        class="app-footer-native__button-text"
        @click="onBackClick"
      >
        返回
      </text>
    </view>

    <view class="app-footer-native__center">
      <template v-if="prompts.length">
        <image
          v-if="isGlasses"
          src="~@assets/images/index/carbon-microphone.png"
          mode="aspectFit"
          class="app-footer-native__center-icon"
        />

        <view class="app-footer-native__prompts-container">
          <text v-if="isGlasses" key="speak" class="app-footer-native__prompt-item app-footer-native__prompt-item--speak">
            请说
          </text>

          <slot>
            <text
              v-for="(item, index) in prompts"
              :key="index"
              :aria-label="item.action ? item.label : ''"
              class="app-footer-native__prompt-item"
              :class="[{ 'app-footer-native__prompt-item--enhanced': !isGlasses, 'app-footer-native__prompt-item--enhanced-primary': !isGlasses && (['primary'].includes(item.type) || index === 0) }]"
              @click="onActionClick(item)"
            >
              {{ isGlasses ? `"${item.label}"` : item.label }}
            </text>
          </slot>
        </view>
      </template>
    </view>

    <view v-if="!hiddenHomeButton" class="app-footer-native__button-wrapper app-footer-native__button-wrapper--right">
      <text
        class="app-footer-native__button-text"
        @click="onHomeClick"
      >
        首页
      </text>
    </view>
  </view>
</template>

<style lang="postcss">
.app-footer-native {
  height: 48rpx;
  flex-direction: row;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: #94a3b8;
}

.app-footer-native--enhanced {
  height: 64rpx !important;
}

.app-footer-native__button-wrapper {
  height: 36rpx;
  width: 96rpx;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.app-footer-native__button-wrapper--left {
  border-right: 0.5rpx solid #444C5D;
}

.app-footer-native__button-wrapper--right {
  border-left: 0.5rpx solid #444C5D;
}

.app-footer-native__button-text {
  font-size: 26rpx;
  color: #94a3b8;
}

.app-footer-native__center {
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.app-footer-native__center-icon {
  width: 32rpx;
  height: 32rpx;
  color: #3a8df7;
}

.app-footer-native__prompts-container {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
}

.app-footer-native__prompt-item {
  font-size: 26rpx;
  color: #3a8df7;
  margin-left: 8rpx;
  lines: 1;
  text-overflow: ellipsis;
  &.app-footer-native__prompt-item--speak {
    margin-left: 0;
  }
}

.app-footer-native__prompt-item--enhanced {
  color: #a9abb1 !important;
  background-color: #282E3B;
  border-radius: 4rpx;
  padding-top: 6rpx;
  padding-bottom: 6rpx;
  padding-left: 16rpx;
  padding-right: 16rpx;

  &.app-footer-native__prompt-item--enhanced-primary {
    background-color: #3A8DF7 !important;
    color: #FFFFFF !important;
  }
}

</style>