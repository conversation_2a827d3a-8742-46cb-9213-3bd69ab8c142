<script setup>
const props = defineProps({
  border: {
    type: Boolean,
    default: false,
  },
  customClass: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <view class="size-full relative overflow-hidden rounded" :class="[border ? 'bg-gradient-to-tr from-[#67718d] to-[#9aa5ba]' : 'bg-[#242a37]']">
    <view v-if="border" class="absolute inset-px bg-[#242a37]/90 rounded"></view>
    <view class="absolute inset-0" :class="customClass">
      <slot />
    </view>
  </view>
</template>

<style>
</style>
