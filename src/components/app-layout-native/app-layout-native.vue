<script>
export default {
  props: {
    prompts: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
  },
}
</script>

<template>
  <view class="app-layout-native">
    <slot name="inset" />

    <view class="app-layout-native__header-wrapper">
      <app-header-native>
        <template #left>
          <slot name="header-left"></slot>
        </template>
        <slot name="header-center"></slot>
      </app-header-native>
    </view>
    <view class="app-layout-native__content-wrapper">
      <slot></slot>
    </view>
    <view class="app-layout-native__footer-wrapper">
      <app-footer-native v-bind="$attrs" :prompts="prompts">
      </app-footer-native>
    </view>
  </view>
</template>

<style lang="postcss">
.app-layout-native {
  flex-direction: column;
  flex: 1;
  position: relative;
  background-color: #12141a;
}

.app-layout-native__header-wrapper {
  position: relative;
  flex: 0;
}
.app-layout-native__content-wrapper {
  flex: 1;
}
.app-layout-native__footer-wrapper {
  flex: 0;
}
</style>
