<script>
export default {
  props: {
  },
  methods: {
  },
}
</script>

<template>
  <wd-config-provider theme="dark" custom-class="size-full flex flex-col overflow-hidden">
    <slot name="inset" />

    <view class="flex-none relative">
      <app-header v-bind="$attrs">
        <template #left>
          <slot name="header-left"></slot>
        </template>
        
        <slot name="header-center"></slot>
      </app-header>
    </view>
    <view class="h-0 flex-1 overflow-hidden relative px-2">
      <view class="flex flex-col size-full">
        <view class="flex-none">
          <slot name="content-top"></slot>
        </view>

        <view class="flex-1 h-0 overflow-hidden">
          <slot></slot>
        </view>

        <view class="flex-none">
          <slot name="content-bottom"></slot>
        </view>
      </view>
    </view>
    <view class="flex-none relative">
      <app-footer v-bind="$attrs">
      </app-footer>
    </view>
  </wd-config-provider>
</template>
