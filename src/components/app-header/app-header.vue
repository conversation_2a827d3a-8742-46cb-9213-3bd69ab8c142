<script setup>
const props = defineProps({
  hiddenHeaderLeft: {
    type: Boolean,
    default: false,
  },
})
/** 修复 APP 端 useNow 兼容性问题 */
function useNowFix({ interval = 0 } = {}) {
  const date = ref(String(new Date()))

  setInterval(() => {
    date.value = String(new Date())
  }, interval)

  return date
}

const nowDate = useDateFormat(useNowFix({ interval: 1000 }), 'MM-DD HH:mm:ss')

const userStore = useUserStore()

async function onLoginToggleClick() {
  if (userStore.workerInfo?.userName) {
    const ret = await uni.showModal({
      title: '提示',
      content: '确定要退出登录吗',
      showCancel: true,
    })

    if (ret.confirm) {
      userStore.logout({ toLogin: true })
    }
    return false
  }

  uni.reLaunch({ url: '/pages/login/index' })
}
</script>

<template>
  <view class="h-6 w-full flex px-2 text-slate-400">
    <view v-if="!hiddenHeaderLeft" class="w-22 flex-none flex items-center">
      <slot name="left">
        <text class="text-xs">
          {{ nowDate }}
        </text>
      </slot>
    </view>

    <view class="w-full flex-1 overflow-hidden h-full">
      <slot></slot>
    </view>

    <view class="w-22 flex flex-none items-center justify-end space-x-1" :aria-label="userStore.workerInfo?.userName ? '退出登录' : '打开登录'" @click="onLoginToggleClick">
      <view class="i-carbon-user-avatar size-4"></view>
      <text class="text-xs">
        {{ userStore.workerInfo?.userName || '未登录' }}
      </text>
    </view>
  </view>
</template>
